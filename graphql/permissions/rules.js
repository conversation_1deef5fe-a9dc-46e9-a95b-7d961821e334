/**
 * GraphQL权限规则定义
 * 定义各种权限检查规则
 */

const { rule, and, or, not } = require('graphql-shield');
const { USER_ROLES, AUTH_TYPES, WHATSAPP_TOKEN_ALLOWED_OPERATIONS } = require('./constants');
const logger = require('../../helpers/logger');

/**
 * JWT认证规则 - 检查是否为标准JWT认证用户
 */
const isJwtAuthenticated = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const authContainer = ctx.req || ctx;
    const isAuth = authContainer.isAuth === true;
    if (!isAuth) {
      logger.warn(`未认证用户尝试访问: ${info.fieldName}`);
    }
    return isAuth || new Error('需要标准的JWT用户认证');
  }
);

/**
 * X-WhatsAppW-Token认证规则 - 仅限制来自web页面的特定操作
 */
const isWebWhatsAppToken = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 检查是否有X-WhatsAppW-Token认证
    const hasWhatsAppAuth = ctx.whatsAppAuth === true && !!ctx.whatsAppCustomerId;

    if (!hasWhatsAppAuth) {
      logger.warn(`无效的X-WhatsAppW-Token尝试访问: ${info.fieldName}`);
      return new Error('需要有效的X-WhatsAppW-Token认证');
    }

    // 检查当前操作是否在允许列表中
    if (!WHATSAPP_TOKEN_ALLOWED_OPERATIONS.includes(info.fieldName)) {
      logger.warn(`X-WhatsAppW-Token尝试访问未授权操作: ${info.fieldName}`);
      return new Error(`X-WhatsAppW-Token不允许访问操作: ${info.fieldName}`);
    }

    logger.debug(`X-WhatsAppW-Token认证成功: ${info.fieldName}, customerId: ${ctx.whatsAppCustomerId}`);
    return true;
  }
);

/**
 * 
 * 内部调用规则 - 允许通过customerId参数的内部调用
 */
const allowInternalCall = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 如果有customerId参数，说明是内部调用，直接允许
    if (args.customerId) {
      logger.debug(`内部调用: ${info.fieldName}, customerId: ${args.customerId}`);
      return true;
    }

    // 否则拒绝
    return new Error('此操作仅允许内部调用');
  }
);

/**
 * 管理员权限规则
 */
const isAdmin = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const authContainer = ctx.req || ctx;
    const isAdminUser = authContainer.userType === USER_ROLES.ADMIN;
    return isAdminUser || new Error('需要管理员权限');
  }
);

/**
 * 餐厅用户权限规则 - 纯粹的餐厅角色检查
 */
const isRestaurant = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const userType = (ctx.req || ctx).userType;
    const isRestaurantUser = userType === USER_ROLES.RESTAURANT;
    return isRestaurantUser || new Error('仅餐厅用户可访问');
  }
);

/**
 * 供应商用户权限规则 - 纯粹的供应商角色检查
 */
const isVendor = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const userType = (ctx.req || ctx).userType;
    const isVendorUser = userType === USER_ROLES.VENDOR;
    return isVendorUser || new Error('仅供应商用户可访问');
  }
);

/**
 * 客户用户权限规则 - 支持JWT和X-WhatsAppW-Token认证的客户
 */
const isCustomer = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const userType = (ctx.req || ctx).userType;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    // JWT认证的客户用户
    const isJwtCustomer = userType === USER_ROLES.CUSTOMER;

    // WhatsApp认证的客户用户
    const isWhatsAppCustomer = userType === USER_ROLES.WHATSAPP_CUSTOMER && !!whatsAppCustomerId && ctx.whatsAppAuth === true;

    const isCustomerUser = isJwtCustomer || isWhatsAppCustomer;



    return isCustomerUser || new Error('仅客户用户可访问');
  }
);

/**
 * WhatsApp客户权限规则 - 专门针对WhatsApp认证的客户
 */
const isWhatsAppCustomer = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx) contexts
    const userType = (ctx.req || ctx).userType;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    const isWhatsAppCustomer = userType === USER_ROLES.WHATSAPP_CUSTOMER &&
                               !!whatsAppCustomerId &&
                               ctx.whatsAppAuth === true;

    if (!isWhatsAppCustomer) {
      logger.warn(`非WhatsApp客户尝试访问WhatsApp专用功能: ${info.fieldName}, 用户类型: ${userType}, WhatsApp认证: ${!!whatsAppCustomerId}`);
    }

    return isWhatsAppCustomer || new Error('仅WhatsApp客户可访问');
  }
);

/**
 * 资源所有权规则 - 检查用户是否有权访问特定资源
 */
const isResourceOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx.currentUser or ctx) contexts
    const authInfo = ctx.req || ctx.currentUser || ctx;
    const { restaurantId, userId, userType } = authInfo;
    
    // 管理员可以访问所有资源
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 餐厅用户只能访问自己的餐厅资源
    if (userType === USER_ROLES.RESTAURANT) {
      // 检查餐厅相关参数
      if (args.restaurant && args.restaurant !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurant}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
      if (args.restaurantId && args.restaurantId !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurantId}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
      return true;
    }

    // 供应商用户可以访问自己拥有的餐厅资源
    if (userType === USER_ROLES.VENDOR) {
      // 供应商需要通过Owner模型检查餐厅所有权
      const requestedRestaurantId = args.restaurant || args.restaurantId;
      if (requestedRestaurantId) {
        try {
          const Owner = require('../../models/owner');
          const owner = await Owner.findById(authInfo.userId);
          if (!owner || !owner.restaurants.includes(requestedRestaurantId)) {
            logger.warn(`供应商用户尝试访问非拥有餐厅资源: ${info.fieldName}, 请求餐厅: ${requestedRestaurantId}, 用户: ${userId}`);
            return new Error('无权访问非拥有餐厅的资源');
          }
        } catch (error) {
          logger.error(`供应商权限检查失败: ${error.message}`);
          return new Error('权限检查失败');
        }
      }
      return true;
    }
    
    // 客户用户只能访问自己的资源
    if (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER) {
      if (args.userId && args.userId !== authInfo.userId) {
        logger.warn(`客户用户尝试访问其他用户资源: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${authInfo.userId}`);
        return new Error('无权访问其他用户的资源');
      }
    }
    
    // 默认拒绝访问以确保安全
    logger.warn(`资源所有权检查未能确定权限: ${info.fieldName}, 用户: ${authInfo.userId}, 类型: ${userType}, 默认拒绝访问`);
    return false;
  }
);

/**
 * 订单所有权规则 - 检查用户是否有权访问特定订单
 */
const isOrderOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    try {
      // Handle both HTTP (ctx.req) and WebSocket (ctx.currentUser or ctx) contexts
      const authInfo = ctx.req || ctx.currentUser || ctx;
      const { restaurantId, userType } = authInfo;
      const whatsAppCustomerId = ctx.whatsAppCustomerId;
      const currentUserId = authInfo.userId || whatsAppCustomerId;

      // 管理员可以访问所有订单
      if (userType === USER_ROLES.ADMIN) {
        return true;
      }

      let order = null;
      // 智能ID查找：优先使用MongoDB _id，然后尝试短ID
      if (args.id) {
        order = await require('../../models/order').findById(args.id).lean();
      } else if (args.orderId) {
        order = await require('../../models/order').findOne({ orderId: args.orderId }).lean();
      }

      // 如果是列表查询或找不到具体订单，则让resolver处理。
      // 在权限层，找不到资源不应视为权限错误。
      if (!order) {
        // 对于列表查询，需要根据用户类型限制查询范围
        // 这里简化处理，直接放行，由resolver的查询逻辑保证数据隔离
        return true;
      }

      // 餐厅用户只能访问自己餐厅的订单
      if (userType === USER_ROLES.RESTAURANT || userType === USER_ROLES.VENDOR) {
        // 对于供应商，理论上应该检查 owner.restaurants，但这里简化为检查 restaurantId
        if (order.restaurantId.toString() !== restaurantId) {
          logger.warn(`餐厅/供应商用户尝试访问其他餐厅订单: ${order.orderId}, 订单餐厅: ${order.restaurantId}, 用户餐厅: ${restaurantId}`);
          return new Error('只能访问自己餐厅的订单');
        }
        return true;
      }

      // 客户用户只能访问自己的订单
      if (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER) {
        if (order.customerId.toString() !== currentUserId) {
          logger.warn(`客户用户尝试访问其他用户订单: ${order.orderId}, 订单客户: ${order.customerId}, 当前用户: ${currentUserId}`);
          return new Error('只能访问自己的订单');
        }
        return true;
      }

      // 其他用户类型默认拒绝
      logger.warn(`未知用户类型尝试访问订单: ${info.fieldName}, 用户: ${currentUserId}, 类型: ${userType}`);
      return new Error('无权访问订单');
    } catch (error) {
      // 捕获可能的CastError，但将其视为权限问题而不是服务器崩溃。
      if (error.name === 'CastError') {
        logger.warn(`订单所有权检查失败: 无效的ID格式, args: ${JSON.stringify(args)}`);
        return new Error('无效的订单ID格式');
      }
      logger.error(`订单所有权检查失败: ${error.message}`, {
        stack: error.stack,
        args: args
      });
      return new Error('订单访问权限检查失败');
    }
  }
);

/**
 * 自我管理规则 - 用户可以管理自己的信息
 */
const isSelfManagement = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // Handle both HTTP (ctx.req) and WebSocket (ctx.currentUser or ctx) contexts
    const authInfo = ctx.req || ctx.currentUser || ctx;
    const { userId, userType } = authInfo;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    // 管理员可以管理所有用户
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }

    // 确定当前用户ID（JWT认证或X-WhatsAppW-Token认证）
    const currentUserId = authInfo.userId || whatsAppCustomerId;

    if (!currentUserId) {
      logger.warn(`自我管理检查无法确定用户身份: ${info.fieldName}, JWT用户: ${userId}, WhatsApp用户: ${whatsAppCustomerId}`);
      return new Error('无法确定用户身份');
    }

    // 用户只能管理自己
    if (args.userId && args.userId !== currentUserId) {
      logger.warn(`用户尝试管理其他用户信息: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${currentUserId}`);
      return new Error('只能管理自己的信息');
    }

    // 对于没有userId参数的操作（如addCustomerAddress），检查是否为自我管理操作
    if (!args.userId) {
      // 对于X-WhatsAppW-Token认证的用户，允许自我管理操作
      if (whatsAppCustomerId) {
        logger.debug(`X-WhatsAppW-Token用户自我管理操作: ${info.fieldName}, 用户: ${whatsAppCustomerId}`);
        return true;
      }

      // 对于JWT认证的用户，也允许自我管理操作
      if (authInfo.userId && (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER || userType === USER_ROLES.RESTAURANT)) {
        logger.debug(`JWT用户自我管理操作: ${info.fieldName}, 用户: ${authInfo.userId}, 类型: ${userType}`);
        return true;
      }

      logger.warn(`自我管理检查失败: ${info.fieldName}, 用户: ${currentUserId}, 类型: ${userType}`);
      return new Error('自我管理权限检查失败');
    }

    return true;
  }
);

module.exports = {
  isJwtAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurant,
  isVendor,
  isCustomer,
  isWhatsAppCustomer,
  isResourceOwner,
  isOrderOwner,
  isSelfManagement
};
