const logger = require('../../helpers/logger');

/**
 * GraphQL Shield 权限日志记录器
 * 提供详细的权限检查日志，帮助调试权限问题
 */

/**
 * 记录权限检查结果
 * @param {string} operation - 操作类型 (Query/Mutation/Subscription)
 * @param {string} fieldName - 字段名称
 * @param {string} ruleName - 规则名称
 * @param {boolean} result - 权限检查结果
 * @param {object} context - GraphQL上下文
 * @param {object} args - 参数
 * @param {string} reason - 拒绝原因（如果被拒绝）
 */
function logPermissionCheck(operation, fieldName, ruleName, result, context, args = {}, reason = null) {
  const { req } = context;
  const userInfo = req ? {
    userId: req.userId || 'anonymous',
    userType: req.userType || 'unknown',
    restaurantId: req.restaurantId || 'none',
    isAuth: req.isAuth || false
  } : { userId: 'no-context', userType: 'unknown', restaurantId: 'none', isAuth: false };

  const logData = {
    operation,
    field: fieldName,
    rule: ruleName,
    result: result ? 'ALLOW' : 'DENY',
    user: userInfo,
    args: Object.keys(args).length > 0 ? args : 'none',
    timestamp: new Date().toISOString()
  };

  if (result) {
    logger.debug(`🟢 SHIELD ALLOW: ${operation}.${fieldName}`, logData);
  } else {
    logger.warn(`🔴 SHIELD DENY: ${operation}.${fieldName}`, {
      ...logData,
      reason: reason || 'Rule returned false',
      suggestion: getSuggestion(fieldName, ruleName, userInfo)
    });
  }
}

/**
 * 记录类型字段权限检查
 * @param {string} typeName - 类型名称
 * @param {string} fieldName - 字段名称
 * @param {boolean} result - 权限检查结果
 * @param {object} context - GraphQL上下文
 */
function logTypeFieldPermission(typeName, fieldName, result, context) {
  const { req } = context;
  const userInfo = req ? {
    userId: req.userId || 'anonymous',
    userType: req.userType || 'unknown'
  } : { userId: 'no-context', userType: 'unknown' };

  const logData = {
    type: typeName,
    field: fieldName,
    result: result ? 'ALLOW' : 'DENY',
    user: userInfo,
    timestamp: new Date().toISOString()
  };

  if (result) {
    logger.debug(`🟢 TYPE FIELD ALLOW: ${typeName}.${fieldName}`, logData);
  } else {
    logger.warn(`🔴 TYPE FIELD DENY: ${typeName}.${fieldName}`, {
      ...logData,
      suggestion: `Add "${typeName}: allow" to permissions.js type section`
    });
  }
}

/**
 * 获取权限被拒绝时的建议
 * @param {string} fieldName - 字段名称
 * @param {string} ruleName - 规则名称
 * @param {object} userInfo - 用户信息
 * @returns {string} 建议信息
 */
function getSuggestion(fieldName, ruleName, userInfo) {
  const suggestions = {
    'isAuthenticated': userInfo.isAuth ? 
      'User is authenticated but rule failed - check rule logic' : 
      'User not authenticated - provide valid JWT token',
    'isAdmin': userInfo.userType !== 'ADMIN' ? 
      'User is not ADMIN - current type: ' + userInfo.userType : 
      'User is ADMIN but rule failed - check rule logic',
    'isRestaurantUser': userInfo.userType !== 'VENDOR' ? 
      'User is not VENDOR - current type: ' + userInfo.userType : 
      'User is VENDOR but rule failed - check rule logic',
    'isCustomerUser': userInfo.userType !== 'default' ? 
      'User is not CUSTOMER - current type: ' + userInfo.userType : 
      'User is CUSTOMER but rule failed - check rule logic'
  };

  return suggestions[ruleName] || `Check rule "${ruleName}" implementation`;
}

/**
 * 记录权限规则执行错误
 * @param {string} ruleName - 规则名称
 * @param {Error} error - 错误对象
 * @param {object} context - GraphQL上下文
 */
function logPermissionError(ruleName, error, context) {
  const { req } = context;
  const userInfo = req ? {
    userId: req.userId || 'anonymous',
    userType: req.userType || 'unknown'
  } : { userId: 'no-context', userType: 'unknown' };

  logger.error(`❌ SHIELD ERROR: Rule "${ruleName}" failed`, {
    rule: ruleName,
    error: error.message,
    stack: error.stack,
    user: userInfo,
    timestamp: new Date().toISOString()
  });
}

/**
 * 记录权限配置缺失
 * @param {string} operation - 操作类型
 * @param {string} fieldName - 字段名称
 * @param {object} context - GraphQL上下文
 */
function logMissingPermission(operation, fieldName, context) {
  const { req } = context;
  const userInfo = req ? {
    userId: req.userId || 'anonymous',
    userType: req.userType || 'unknown'
  } : { userId: 'no-context', userType: 'unknown' };

  logger.warn(`⚠️ SHIELD MISSING: No permission rule for ${operation}.${fieldName}`, {
    operation,
    field: fieldName,
    user: userInfo,
    suggestion: `Add permission rule for "${fieldName}" in permissions.js`,
    timestamp: new Date().toISOString()
  });
}

module.exports = {
  logPermissionCheck,
  logTypeFieldPermission,
  logPermissionError,
  logMissingPermission,
  getSuggestion
};
