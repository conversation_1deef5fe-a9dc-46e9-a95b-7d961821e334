const mongoose = require('mongoose');
const { REFUND_REASON, REFUND_STATUS, REFUND_TYPE } = require('../helpers/enum');

const Schema = mongoose.Schema;

const refundSchema = new Schema(
  {
    refundId: {
      type: String,
      required: true,
      unique: true
    },
    orderId: {
      type: String,
      required: true
    },
    originalOrderId: {
      type: Schema.Types.ObjectId,
      ref: 'Order',
      required: true
    },
    refundType: {
      type: String,
      enum: Object.values(REFUND_TYPE),
      required: true
    },
    requestAmount: {
      type: Number,
      required: true,
      min: 0
    },
    finalRefundAmount: {
      type: Number,
      required: true,
      min: 0
    },
    reason: {
      type: String,
      enum: Object.values(REFUND_REASON),
      required: true
    },
    reasonText: {
      type: String,
      maxlength: 500,
      trim: true
    },
    feeBearer: {
      type: String,
      enum: ['MERCHANT', 'CUSTOMER'],
      required: true
    },
    transactionFee: {
      type: Number,
      default: 0,
      min: 0
    },
    status: {
      type: String,
      enum: Object.values(REFUND_STATUS),
      default: REFUND_STATUS.PENDING
    },
    stripeRefundId: {
      type: String,
      sparse: true // 允许多个null值，但非null值必须唯一
    },
    requestedBy: {
      type: Schema.Types.ObjectId,
      ref: 'Restaurant',
      required: true
    },
    processedAt: {
      type: Date
    },
    completedAt: {
      type: Date
    },
    errorMessage: {
      type: String
    },
    metadata: {
      type: Object,
      default: {}
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// 索引
refundSchema.index({ refundId: 1 });
refundSchema.index({ orderId: 1 });
refundSchema.index({ originalOrderId: 1 });
refundSchema.index({ stripeRefundId: 1 });
refundSchema.index({ status: 1 });
refundSchema.index({ createdAt: -1 });

// 虚拟字段
refundSchema.virtual('isCompleted').get(function() {
  return this.status === REFUND_STATUS.SUCCEEDED;
});

refundSchema.virtual('isFailed').get(function() {
  return this.status === REFUND_STATUS.FAILED;
});

refundSchema.virtual('isPending').get(function() {
  return this.status === REFUND_STATUS.PENDING || this.status === REFUND_STATUS.PROCESSING;
});

// 实例方法
refundSchema.methods.markAsProcessing = function() {
  this.status = REFUND_STATUS.PROCESSING;
  this.processedAt = new Date();
  return this.save();
};

refundSchema.methods.markAsSucceeded = function() {
  this.status = REFUND_STATUS.SUCCEEDED;
  this.completedAt = new Date();
  return this.save();
};

refundSchema.methods.markAsFailed = function(errorMessage) {
  this.status = REFUND_STATUS.FAILED;
  this.completedAt = new Date();
  this.errorMessage = errorMessage;
  return this.save();
};

// 静态方法
refundSchema.statics.findByOrderId = function(orderId) {
  return this.find({ orderId }).sort({ createdAt: -1 });
};

refundSchema.statics.findByStripeRefundId = function(stripeRefundId) {
  return this.findOne({ stripeRefundId });
};

refundSchema.statics.generateRefundId = function(orderId) {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `REF-${orderId}-${timestamp}-${random}`.toUpperCase();
};

// 中间件
refundSchema.pre('save', function(next) {
  // 确保orderId存在
  if (!this.orderId) {
    return next(new Error('orderId is required for refund ID generation'));
  }

  // 生成refundId
  if (!this.refundId) {
    this.refundId = this.constructor.generateRefundId(this.orderId);
  }

  // 验证退款金额
  if (this.finalRefundAmount > this.requestAmount) {
    return next(new Error('Final refund amount cannot exceed request amount'));
  }

  next();
});

refundSchema.pre('validate', function(next) {
  // 部分退款必须提供原因文本
  if (this.refundType === REFUND_TYPE.PARTIAL && this.reason === REFUND_REASON.MERCHANT_OTHER && !this.reasonText) {
    return next(new Error('Reason text is required for partial refunds with MERCHANT_OTHER reason'));
  }
  
  next();
});

module.exports = mongoose.model('Refund', refundSchema);
