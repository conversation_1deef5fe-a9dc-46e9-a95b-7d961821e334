{"name": "firespoon-api", "version": "2.0.0", "description": "Firespoon API", "main": "app.js", "scripts": {"test": "NODE_ENV=test jest --config test/config/jest.config.js", "test:watch": "NODE_ENV=test jest --config test/config/jest.config.js --watch", "test:coverage": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects UNIT --coverage", "test:unit": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects UNIT", "test:integration": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects INTEGRATION", "test:e2e": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects E2E", "test:performance": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects PERFORMANCE", "test:all": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects UNIT INTEGRATION E2E", "test:ci": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects UNIT INTEGRATION --ci --coverage --watchAll=false", "test:unit:watch": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects UNIT --watch", "test:integration:watch": "NODE_ENV=test jest --config test/config/jest.config.js --selectProjects INTEGRATION --watch", "prestart": "node seed/user-seeder.js && node seed/configuration-seeder.js", "start": "NODE_ENV=production node app.js", "local": "NODE_ENV=local nodemon app.js", "render": "NODE_ENV=render nodemon app.js", "prod": "NODE_ENV=production node app.js", "format": "prettier --write '**/*.js'", "lint:fix": "eslint . --ext .js --fix", "generate": "graphql-codegen"}, "husky": {"hooks": {"pre-commit": "lint-staged"}, "ignore": ["build", "build-nov17"]}, "lint-staged": {"*.js": ["npm run format", "npm run lint:fix"]}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.0", "@graphql-tools/load-files": "^7.0.0", "@graphql-tools/merge": "^9.0.0", "@graphql-tools/schema": "^8.3.1", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^6.19.7", "@sentry/tracing": "^6.19.7", "apollo-server-express": "^3.6.0", "base64url": "^3.0.1", "bcryptjs": "^2.4.3", "body-parser": "^1.18.3", "bull": "^4.12.2", "consolidate": "^0.15.1", "dotenv": "^10.0.0", "ejs": "^3.1.6", "expo-server-sdk": "^3.6.0", "express": "^4.17.2", "express-session": "^1.17.3", "firebase-admin": "^10.0.1", "form-data": "^4.0.2", "graphql": "^16.2.0", "graphql-request": "^7.2.0", "graphql-shield": "^7.6.5", "graphql-subscriptions": "^2.0.0", "graphql-voyager": "^1.0.0-rc.31", "jsonwebtoken": "^8.5.1", "mongoose": "^6.1.4", "nodemailer": "^6.7.2", "otp-generator": "^4.0.0", "paypal-rest-sdk": "^1.8.1", "randomstring": "^1.2.1", "redis": "^4.6.0", "stripe": "^18.0.0", "subscriptions-transport-ws": "^0.11.0", "twilio": "^3.73.0", "uuid": "^8.3.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-async-to-generator": "^7.25.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.23.9", "@babel/runtime": "^7.26.9", "@faker-js/faker": "^8.4.1", "@graphql-codegen/cli": "^5.0.4", "@graphql-codegen/typescript": "^4.1.3", "@graphql-codegen/typescript-mongodb": "^3.0.0", "@graphql-codegen/typescript-resolvers": "^4.4.1", "@graphql-tools/graphql-file-loader": "^8.0.12", "@jest/globals": "^29.7.0", "apollo-server-testing": "^2.25.3", "autocannon": "^7.15.0", "babel-jest": "^29.7.0", "eslint": "^7.1.0", "eslint-config-prettier": "^10.1.2", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.20.0", "eslint-plugin-standard": "^4.0.1", "graphql-middleware": "^6.1.35", "husky": "^4.2.5", "jest": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^10.2.7", "metro-react-native-babel-preset": "^0.54.1", "nock": "^13.5.4", "nodemon": "^1.19.4", "prettier": "2.0.5", "prettier-config-standard": "^1.0.1", "supertest": "^6.3.4", "testcontainers": "^10.7.2"}}