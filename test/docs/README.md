# Testing Documentation

This directory contains comprehensive testing documentation for the Firespoon API project, organized by category for easy navigation.

## 📁 Directory Structure

```
test/
├── docs/                # Testing documentation
│   ├── testing-guide.md      # Comprehensive testing methodology (中文)
│   ├── best-practices.md     # Standards and conventions for writing tests
│   ├── coverage-guide.md     # Coverage targets, reporting, and analysis
│   ├── integration/          # Integration testing specific documentation
│   │   ├── framework_integration_guide.md
│   │   ├── questions_and_answers.md
│   │   ├── refund_testing_summary.md
│   │   ├── test_integration_plan.md
│   │   └── whatsapp/         # WhatsApp integration docs
│   ├── README.md            # This file
│   └── index.html           # Web interface
└── reports/             # Test execution reports and status
    ├── implementation-summary.md     # Complete testing implementation overview
    ├── implementation-corrections.md # Corrections and improvements made
    ├── completion-status.md          # Current testing progress and status
    ├── remaining-issues.md           # Outstanding issues and resolution plans
    └── integration-test-report.md    # Integration testing report
```

## 📚 Quick Access

### 🎯 Getting Started
- **[Testing Guide](testing-guide.md)** - Start here for comprehensive testing methodology
- **[Best Practices](best-practices.md)** - Essential standards for writing quality tests

### 📊 Coverage & Analysis
- **[Coverage Guide](coverage-guide.md)** - Understanding and improving test coverage
- **[Completion Status](../reports/completion-status.md)** - Current testing progress

### 📝 Implementation Reports
- **[Implementation Summary](../reports/implementation-summary.md)** - Complete overview of testing implementation
- **[Integration Test Report](../reports/integration-test-report.md)** - Detailed integration testing results

## 🎯 Testing Strategy Overview

The Firespoon API testing strategy includes:

### Test Types
1. **Unit Tests** - Individual component testing
2. **Integration Tests** - Component interaction testing
3. **End-to-End Tests** - Complete workflow testing
4. **Performance Tests** - Load and stress testing

### Test Coverage Targets
- **Statements**: > 80%
- **Branches**: > 75%
- **Functions**: > 85%
- **Lines**: > 80%

### Key Testing Areas
- **GraphQL API** - Query and mutation testing
- **WhatsApp Integration** - Conversation flow testing
- **Payment Processing** - Stripe and PayPal integration testing
- **Authentication** - JWT and session management testing
- **Database Operations** - Data persistence and retrieval testing

## 🚀 Quick Start

### Running Tests
```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e

# Generate coverage report
npm run test:coverage
```

### Test Environment Setup
1. Install dependencies: `npm install`
2. Configure test environment variables
3. Start test databases (MongoDB, Redis)
4. Run test suite

## 🔧 Integration Testing

For integration-specific documentation:
- **[Integration Guide](integration/framework_integration_guide.md)** - Framework integration guidelines
- **[Q&A](integration/questions_and_answers.md)** - Common integration testing questions
- **[Refund Testing](integration/refund_testing_summary.md)** - Refund system testing summary

## 📖 Related Documentation

- **[Main Test README](../README.md)** - Main testing framework documentation
- **[Test Cases](../cases/)** - YAML test case definitions
- **[Test Reports](../reports/)** - Test execution reports and status
- **[Test Helpers](../helpers/)** - Testing utility functions
- **[Test Factories](../factories/)** - Test data generation

## 🔄 Maintenance Guidelines

### When to Update Documentation
- ✅ Adding new test types or categories
- ✅ Changing testing frameworks or tools
- ✅ Updating coverage targets or standards
- ✅ Implementing new testing best practices
- ✅ Resolving testing issues or bugs

### Documentation Standards
- Use clear, descriptive headings
- Include code examples where applicable
- Keep links up-to-date when moving files
- Use consistent formatting and style
- Update the main README when adding new documents

### Review Schedule
- **Weekly**: Update completion status and progress reports
- **Monthly**: Review and update best practices
- **Quarterly**: Comprehensive documentation review and cleanup

## 📝 Recent Updates

### 2025-08-15: X-WhatsAppW-Token 权限修复
- **问题**: X-WhatsAppW-Token 认证用户无法执行自我管理操作（如 `addCustomerAddress`）
- **修复**: 更新 `isCustomer` 和 `isSelfManagement` 权限规则，支持双重认证方式
- **影响**: 修复了 Web 客户端通过 WhatsApp Token 添加地址等操作的权限问题
- **文档**: 更新了 [GraphQL权限测试用例文档](graphql-permissions-test-cases.md) 中的修复记录

### 2025-08-15: 订单权限逻辑修复
- **问题**: 订单权限逻辑中字段名不匹配，`isOrderOwner` 规则未实现
- **修复**: 修正字段名（`order.restaurant` → `order.restaurantId`），实现完整的订单权限检查
- **测试**: 添加了 6 个订单权限集成测试用例，全部通过
- **权限**: 确保餐厅用户、客户和管理员都能正确访问相关订单

---

*For detailed testing procedures and implementation guides, refer to the documents in the respective subdirectories.*
