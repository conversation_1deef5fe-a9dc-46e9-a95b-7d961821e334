/**
 * GraphQL权限控制综合测试
 * 测试所有新增的权限配置和操作
 */

// Mock logger before importing rules
jest.mock('../../../helpers/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

// Mock graphql-shield logger
jest.mock('graphql-shield', () => {
  const originalModule = jest.requireActual('graphql-shield');
  return {
    ...originalModule,
    rule: (name, func, options = {}) => {
      return originalModule.rule(name, func, {
        ...options,
        debug: false
      });
    }
  };
});

const {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser,
  isUserManager,
  isSensitiveConfigurationAccess
} = require('../../../graphql/permissions/rules');

const { 
  WHATSAPP_TOKEN_ALLOWED_OPERATIONS,
  PUBLIC_OPERATIONS,
  SENSITIVE_OPERATIONS
} = require('../../../graphql/permissions/constants');

describe('GraphQL权限控制综合测试', () => {
  // 模拟GraphQL info对象
  const createMockInfo = (fieldName) => ({
    fieldName,
    operation: {
      operation: fieldName.startsWith('get') || fieldName.includes('query') ? 'query' : 'mutation'
    }
  });

  describe('公开查询权限测试', () => {
    test('所有公开查询应该无需认证即可访问', async () => {
      const publicQueries = PUBLIC_OPERATIONS.QUERIES;
      
      for (const query of publicQueries) {
        // 公开查询在permissions.js中设置为true，不需要特殊测试
        // 这里主要验证常量定义的正确性
        expect(publicQueries).toContain(query);
      }
    });

    test('公开变更应该无需认证即可访问', async () => {
      const publicMutations = PUBLIC_OPERATIONS.MUTATIONS;
      
      // 验证关键的公开变更
      expect(publicMutations).toContain('createUser');
      expect(publicMutations).toContain('login');
      expect(publicMutations).toContain('adminLogin');
      expect(publicMutations).toContain('restaurantLogin');
      expect(publicMutations).toContain('riderLogin');
      expect(publicMutations).toContain('forgotPassword');
      expect(publicMutations).toContain('resetPassword');
    });
  });

  describe('WhatsApp Token权限测试', () => {
    test('应该允许WhatsApp Token访问所有允许的操作', async () => {
      const mockContext = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'test-customer-id'
      };

      for (const operation of WHATSAPP_TOKEN_ALLOWED_OPERATIONS) {
        const result = await isWebWhatsAppToken.resolve(null, {}, mockContext, createMockInfo(operation));
        expect(result).toBe(true);
      }
    });

    test('WhatsApp Token允许的操作应该包含正确的查询', async () => {
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('customerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getAddressFromPostcode');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getSessionByToken');
    });

    test('WhatsApp Token允许的操作应该包含正确的变更', async () => {
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('placeOrderWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('addCustomerAddress');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('deleteCustomerAddress');
    });
  });

  describe('管理员权限测试', () => {
    test('管理员应该能访问所有敏感查询', async () => {
      const mockContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const sensitiveQueries = SENSITIVE_OPERATIONS.QUERIES;
      
      // 验证敏感查询包含管理员专用操作
      expect(sensitiveQueries).toContain('users');
      expect(sensitiveQueries).toContain('configuration');
      expect(sensitiveQueries).toContain('allOrders');
      expect(sensitiveQueries).toContain('riders');
      expect(sensitiveQueries).toContain('earnings');
    });

    test('管理员应该能访问所有敏感变更', async () => {
      const sensitiveOperations = SENSITIVE_OPERATIONS.MUTATIONS;
      
      // 验证敏感变更包含管理员专用操作
      expect(sensitiveOperations).toContain('createVendor');
      expect(sensitiveOperations).toContain('createRider');
      expect(sensitiveOperations).toContain('createBrand');
      expect(sensitiveOperations).toContain('updateCommission');
      expect(sensitiveOperations).toContain('saveConfiguration');
    });

    test('isAdmin规则应该正确验证管理员权限', async () => {
      const adminContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const result = await isAdmin.resolve(null, {}, adminContext, createMockInfo('adminQuery'));
      expect(result).toBe(true);
    });

    test('isAdmin规则应该拒绝非管理员用户', async () => {
      const userContext = {
        req: {
          isAuth: true,
          userType: 'CUSTOMER'
        }
      };

      const result = await isAdmin.resolve(null, {}, userContext, createMockInfo('adminQuery'));
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('需要管理员权限');
    });
  });

  describe('餐厅用户权限测试', () => {
    test('餐厅用户应该能访问餐厅相关操作', async () => {
      const restaurantContext = {
        req: {
          isAuth: true,
          userType: 'RESTAURANT'
        }
      };

      const result = await isRestaurantUser.resolve(null, {}, restaurantContext, createMockInfo('restaurantQuery'));
      expect(result).toBe(true);
    });

    test('管理员应该也能访问餐厅操作', async () => {
      const adminContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const result = await isRestaurantUser.resolve(null, {}, adminContext, createMockInfo('restaurantQuery'));
      expect(result).toBe(true);
    });

    test('客户用户不应该能访问餐厅操作', async () => {
      const customerContext = {
        req: {
          isAuth: true,
          userType: 'CUSTOMER'
        }
      };

      const result = await isRestaurantUser.resolve(null, {}, customerContext, createMockInfo('restaurantQuery'));
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('需要餐厅用户权限');
    });
  });

  describe('客户用户权限测试', () => {
    test('客户用户应该能访问客户相关操作', async () => {
      const customerContext = {
        req: {
          isAuth: true,
          userType: 'CUSTOMER'
        }
      };

      const result = await isCustomerUser.resolve(null, {}, customerContext, createMockInfo('customerQuery'));
      expect(result).toBe(true);
    });

    test('管理员应该也能访问客户操作', async () => {
      const adminContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const result = await isCustomerUser.resolve(null, {}, adminContext, createMockInfo('customerQuery'));
      expect(result).toBe(true);
    });
  });

  describe('内部调用权限测试', () => {
    test('内部调用应该被允许', async () => {
      const internalContext = {
        internalCall: true
      };

      const result = await allowInternalCall.resolve(null, {}, internalContext, createMockInfo('internalQuery'));
      expect(result).toBe(true);
    });

    test('非内部调用应该被拒绝', async () => {
      const externalContext = {
        internalCall: false
      };

      const result = await allowInternalCall.resolve(null, {}, externalContext, createMockInfo('internalQuery'));
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('仅允许内部调用');
    });
  });

  describe('用户管理权限测试', () => {
    test('用户管理员应该能访问用户管理功能', async () => {
      const userManagerContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const result = await isUserManager.resolve(null, {}, userManagerContext, createMockInfo('userManagementQuery'));
      expect(result).toBe(true);
    });
  });

  describe('敏感配置权限测试', () => {
    test('管理员应该能访问敏感配置', async () => {
      const adminContext = {
        req: {
          isAuth: true,
          userType: 'ADMIN'
        }
      };

      const result = await isSensitiveConfigurationAccess.resolve(null, {}, adminContext, createMockInfo('configQuery'));
      expect(result).toBe(true);
    });

    test('非管理员不应该能访问敏感配置', async () => {
      const userContext = {
        req: {
          isAuth: true,
          userType: 'CUSTOMER'
        }
      };

      const result = await isSensitiveConfigurationAccess.resolve(null, {}, userContext, createMockInfo('configQuery'));
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('需要管理员权限');
    });
  });

  describe('权限常量完整性测试', () => {
    test('WhatsApp Token允许的操作不应该包含已移除的操作', async () => {
      // 确保不包含已移除的操作
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('placeOrderFromWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getCustomerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getRestaurantMenuForCustomer');
    });

    test('敏感操作不应该包含已移除的操作', async () => {
      // 确保不包含已移除的操作
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('allUsers');
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('getRestaurantMenuForCustomer');
    });

    test('公开操作应该包含所有新增的公开查询', async () => {
      const publicQueries = PUBLIC_OPERATIONS.QUERIES;
      
      // 验证包含新增的公开查询
      expect(publicQueries).toContain('restaurantList');
      expect(publicQueries).toContain('foodByIds');
      expect(publicQueries).toContain('nearByRestaurants');
      expect(publicQueries).toContain('topRatedVendors');
      expect(publicQueries).toContain('brand');
      expect(publicQueries).toContain('brands');
    });
  });
});
