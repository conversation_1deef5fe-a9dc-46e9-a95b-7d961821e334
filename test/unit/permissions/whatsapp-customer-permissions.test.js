/**
 * WhatsApp客户权限测试
 * 测试新的WHATSAPP_CUSTOMER用户类型权限
 */

const { USER_ROLES } = require('../../../graphql/permissions/constants');

describe('WhatsApp Customer Permissions', () => {
  describe('USER_ROLES Constants', () => {
    test('should have WHATSAPP_CUSTOMER role defined', () => {
      expect(USER_ROLES.WHATSAPP_CUSTOMER).toBe('whatsapp_customer');
    });

    test('should maintain existing CUSTOMER role', () => {
      expect(USER_ROLES.CUSTOMER).toBe('default');
    });

    test('should have all required roles', () => {
      expect(USER_ROLES).toHaveProperty('ADMIN');
      expect(USER_ROLES).toHaveProperty('RESTAURANT');
      expect(USER_ROLES).toHaveProperty('CUSTOMER');
      expect(USER_ROLES).toHaveProperty('WHATSAPP_CUSTOMER');
      expect(USER_ROLES).toHaveProperty('RIDER');
      expect(USER_ROLES).toHaveProperty('VENDOR');
    });
  });

  describe('WhatsApp Authentication Context', () => {
    test('should set correct userType for WhatsApp customers', () => {
      // 模拟WhatsApp认证后的上下文
      const mockContext = {
        req: {
          userId: 'customer123',
          userType: USER_ROLES.WHATSAPP_CUSTOMER
        },
        whatsAppAuth: true,
        whatsAppCustomerId: 'customer123'
      };

      expect(mockContext.req.userType).toBe('whatsapp_customer');
      expect(mockContext.whatsAppAuth).toBe(true);
      expect(mockContext.whatsAppCustomerId).toBe('customer123');
    });
  });

  describe('Permission Differentiation', () => {
    test('should differentiate between regular and WhatsApp customers', () => {
      const regularCustomer = {
        userType: USER_ROLES.CUSTOMER,
        authType: 'JWT'
      };

      const whatsappCustomer = {
        userType: USER_ROLES.WHATSAPP_CUSTOMER,
        authType: 'WEB_WHATSAPP'
      };

      expect(regularCustomer.userType).not.toBe(whatsappCustomer.userType);
      expect(regularCustomer.authType).not.toBe(whatsappCustomer.authType);
    });
  });
});
