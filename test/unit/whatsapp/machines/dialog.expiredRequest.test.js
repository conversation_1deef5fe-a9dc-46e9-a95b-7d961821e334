const DialogManager = require('../../../../whatsapp/machines/dialog');
const whatsappService = require('../../../../whatsapp/services/whatsappService');
const orderFsmActions = require('../../../../whatsapp/machines/orderFsmActions');
const logger = require('../../../../helpers/logger');

// Mock dependencies
jest.mock('../../../../whatsapp/services/whatsappService');
jest.mock('../../../../whatsapp/machines/orderFsmActions');
jest.mock('../../../../helpers/logger');

describe('DialogManager - Expired Request Handling', () => {
  let mockSession;
  let mockContext;

  beforeEach(() => {
    // Mock session object
    mockSession = {
      dialogueId: 'test-dialogue-123',
      id: 'test-session-token',
      context: {
        customer: {
          customerId: 'test-customer-456',
          name: 'Test Customer',
          phone: '+1234567890'
        },
        selectedRestaurantRef: {
          id: 'test-restaurant-789',
          name: 'Test Restaurant'
        },
        isRestaurantSelected: true,
        isAddressSelected: false
      }
    };

    // Mock context
    mockContext = { ...mockSession.context };

    // Clear all mocks
    jest.clearAllMocks();
    
    // Mock logger methods
    logger.debug = jest.fn();
    logger.warn = jest.fn();
    logger.error = jest.fn();
    logger.info = jest.fn();

    // Mock whatsappService methods
    whatsappService.sendDialogueText = jest.fn().mockResolvedValue(true);

    // Mock orderFsmActions methods
    orderFsmActions.sendWelcomeMessage = jest.fn().mockResolvedValue(true);
  });

  describe('handleExpiredRequest', () => {
    test('should handle expired order operation request', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order',
        messageId: 'test-message-001',
        messageType: 'TEXT'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(logger.debug).toHaveBeenCalledWith('Processing expired request', {
        dialogueId: mockSession.dialogueId,
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      });
    });

    test('should send correct expired message for order operation', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Your order has expired. Please place a new order.'
      );
    });

    test('should send generic expired message for unknown request type', async () => {
      // Arrange
      const eventData = {
        externalID: 'unknown_operation',
        requestType: 'UNKNOWN_TYPE',
        messageContent: 'Unknown Operation'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Your request has expired. Please try again.'
      );
    });

    test('should send welcome message after expired message', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(orderFsmActions.sendWelcomeMessage).toHaveBeenCalledWith(
        mockSession,
        mockContext
      );
    });

    test('should log successful handling', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(logger.debug).toHaveBeenCalledWith('Expired request handled successfully', {
        dialogueId: mockSession.dialogueId,
        externalID: 'confirm_order'
      });
    });

    test('should handle cancel_order expired request', async () => {
      // Arrange
      const eventData = {
        externalID: 'cancel_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Cancel Order'
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Your order has expired. Please place a new order.'
      );
      expect(orderFsmActions.sendWelcomeMessage).toHaveBeenCalledWith(
        mockSession,
        mockContext
      );
    });

    test('should handle errors gracefully', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      };
      const error = new Error('Test error');
      whatsappService.sendDialogueText.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(
        DialogManager.handleExpiredRequest(mockSession, eventData, mockContext)
      ).rejects.toThrow('Test error');

      expect(logger.error).toHaveBeenCalledWith('Error in handleExpiredRequest', {
        dialogueId: mockSession.dialogueId,
        externalID: 'confirm_order',
        err: error.message,
        stack: error.stack
      });
    });

    test('should send fallback error message on failure', async () => {
      // Arrange
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: 'Confirm Order'
      };
      const error = new Error('Test error');
      whatsappService.sendDialogueText
        .mockResolvedValueOnce(true) // First call succeeds (expired message)
        .mockRejectedValueOnce(error) // Second call fails (welcome message)
        .mockResolvedValueOnce(true); // Third call succeeds (fallback message)

      orderFsmActions.sendWelcomeMessage.mockRejectedValueOnce(error);

      // Act
      try {
        await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);
      } catch (e) {
        // Expected to throw
      }

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Sorry, we encountered an error. Please try again later.'
      );
    });

    test('should handle missing eventData gracefully', async () => {
      // Arrange
      const eventData = {}; // Empty event data

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Your request has expired. Please try again.'
      );
      expect(orderFsmActions.sendWelcomeMessage).toHaveBeenCalled();
    });

    test('should truncate long message content in logs', async () => {
      // Arrange
      const longMessage = 'A'.repeat(100); // 100 character message
      const eventData = {
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: longMessage
      };

      // Act
      await DialogManager.handleExpiredRequest(mockSession, eventData, mockContext);

      // Assert
      expect(logger.debug).toHaveBeenCalledWith('Processing expired request', {
        dialogueId: mockSession.dialogueId,
        externalID: 'confirm_order',
        requestType: 'ORDER_OPERATION',
        messageContent: longMessage.substring(0, 50)
      });
    });
  });

  describe('processSessionEvent - EXPIRED_REQUEST', () => {
    test('should process EXPIRED_REQUEST event type', async () => {
      // Arrange
      const event = {
        type: 'EXPIRED_REQUEST',
        data: {
          externalID: 'confirm_order',
          requestType: 'ORDER_OPERATION',
          messageContent: 'Confirm Order',
          dialogueId: mockSession.dialogueId
        }
      };

      // Mock sessionService.updateSession
      const sessionService = require('../../../../whatsapp/services/sessionService');
      sessionService.updateSession = jest.fn().mockResolvedValue(true);

      // Act
      await DialogManager.processSessionEvent(mockSession, event);

      // Assert
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockSession.dialogueId,
        'Your order has expired. Please place a new order.'
      );
      expect(orderFsmActions.sendWelcomeMessage).toHaveBeenCalled();
    });
  });
});
