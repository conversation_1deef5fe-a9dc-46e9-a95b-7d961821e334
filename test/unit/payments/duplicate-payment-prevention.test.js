/**
 * 防重复支付测试
 * 测试支付系统的防重复支付机制
 */

const mongoose = require('mongoose');
const Order = require('../../../models/order');
const Stripe = require('../../../models/stripe');
const Paypal = require('../../../models/paypal');

// Mock logger
const mockLogger = {
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Mock Stripe
const mockStripe = {
  checkout: {
    sessions: {
      retrieve: jest.fn()
    }
  }
};

describe('Duplicate Payment Prevention', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Stripe Duplicate Payment Prevention', () => {
    test('should detect existing order and prevent duplicate creation', async () => {
      const orderId = 'test-order-123';
      const stripeSessionId = 'cs_test_session_123';

      // Mock existing order
      const existingOrder = {
        _id: new mongoose.Types.ObjectId(),
        orderId,
        paymentStatus: 'PAID',
        orderAmount: 25.50
      };

      // Mock Stripe order
      const stripeOrder = {
        orderId,
        stripeSessionId,
        orderAmount: 25.50
      };

      // Mock database queries
      jest.spyOn(Stripe, 'findOne').mockResolvedValue(stripeOrder);
      jest.spyOn(Order, 'findOne').mockResolvedValue(existingOrder);

      // Test the logic (simulated)
      const result = await simulateHandleCompletedCheckoutSession(stripeSessionId);

      expect(result).toBe(existingOrder);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Duplicate payment attempt detected',
        expect.objectContaining({
          orderId,
          stripeSessionId,
          existingOrderId: existingOrder._id,
          existingPaymentStatus: existingOrder.paymentStatus
        })
      );
    });

    test('should verify payment amount matches expected amount', async () => {
      const orderId = 'test-order-456';
      const stripeSessionId = 'cs_test_session_456';
      const expectedAmount = 25.50;
      const paidAmount = 25.50;

      // Mock Stripe session
      const mockSession = {
        payment_status: 'paid',
        amount_total: Math.round(paidAmount * 100) // Stripe uses cents
      };

      mockStripe.checkout.sessions.retrieve.mockResolvedValue(mockSession);

      // Test amount verification logic
      const amountVerification = verifyPaymentAmount(mockSession, expectedAmount);

      expect(amountVerification.isValid).toBe(true);
      expect(amountVerification.paidAmount).toBe(paidAmount);
      expect(amountVerification.expectedAmount).toBe(expectedAmount);
    });

    test('should detect payment amount mismatch', async () => {
      const expectedAmount = 25.50;
      const paidAmount = 20.00; // Different amount

      // Mock Stripe session with mismatched amount
      const mockSession = {
        payment_status: 'paid',
        amount_total: Math.round(paidAmount * 100)
      };

      mockStripe.checkout.sessions.retrieve.mockResolvedValue(mockSession);

      // Test amount verification logic
      const amountVerification = verifyPaymentAmount(mockSession, expectedAmount);

      expect(amountVerification.isValid).toBe(false);
      expect(amountVerification.difference).toBe(paidAmount - expectedAmount);
    });
  });

  describe('PayPal Duplicate Payment Prevention', () => {
    test('should detect existing PayPal order and prevent duplicate creation', async () => {
      const orderId = 'paypal-order-789';
      const paymentId = 'PAYID-TEST123';

      // Mock existing order
      const existingOrder = {
        _id: new mongoose.Types.ObjectId(),
        orderId,
        paymentStatus: 'PAID',
        orderAmount: 30.75
      };

      // Mock PayPal order
      const paypalOrder = {
        orderId,
        paymentId,
        orderAmount: 30.75
      };

      // Mock database queries
      jest.spyOn(Paypal, 'findOne').mockResolvedValue(paypalOrder);
      jest.spyOn(Order, 'findOne').mockResolvedValue(existingOrder);

      // Test the logic (simulated)
      const result = await simulatePayPalDuplicateCheck(orderId);

      expect(result.isDuplicate).toBe(true);
      expect(result.existingOrder).toBe(existingOrder);
    });

    test('should verify PayPal payment amount', async () => {
      const expectedAmount = 30.75;
      const paidAmount = 30.75;

      // Mock PayPal payment response
      const mockPayment = {
        state: 'approved',
        transactions: [{
          amount: {
            total: paidAmount.toString()
          }
        }]
      };

      // Test amount verification
      const verification = verifyPayPalAmount(mockPayment, expectedAmount);

      expect(verification.isValid).toBe(true);
      expect(verification.paidAmount).toBe(paidAmount);
      expect(verification.expectedAmount).toBe(expectedAmount);
    });
  });

  describe('General Payment Security', () => {
    test('should handle edge cases in amount comparison', () => {
      // Test floating point precision issues
      const amount1 = 25.50;
      const amount2 = 25.499999999; // Should be considered equal

      const isEqual = Math.abs(amount1 - amount2) <= 0.01;
      expect(isEqual).toBe(true);
    });

    test('should reject payments with significant amount differences', () => {
      const expectedAmount = 25.50;
      const paidAmount = 20.00;

      const difference = Math.abs(expectedAmount - paidAmount);
      const isValid = difference <= 0.01;

      expect(isValid).toBe(false);
      expect(difference).toBe(5.50);
    });
  });
});

// Helper functions to simulate the actual logic
async function simulateHandleCompletedCheckoutSession(stripeSessionId) {
  const stripeOrder = await Stripe.findOne({ stripeSessionId });
  if (!stripeOrder) return null;

  const existingOrder = await Order.findOne({ orderId: stripeOrder.orderId });
  if (existingOrder) {
    mockLogger.warn('Duplicate payment attempt detected', {
      orderId: stripeOrder.orderId,
      stripeSessionId,
      existingOrderId: existingOrder._id,
      existingPaymentStatus: existingOrder.paymentStatus
    });
    return existingOrder;
  }

  return null;
}

async function simulatePayPalDuplicateCheck(orderId) {
  const existingOrder = await Order.findOne({ orderId });
  
  return {
    isDuplicate: !!existingOrder,
    existingOrder
  };
}

function verifyPaymentAmount(stripeSession, expectedAmount) {
  const CURRENCY_MULTIPLIER = 100;
  const paidAmountInCents = stripeSession.amount_total;
  const expectedAmountInCents = Math.round(expectedAmount * CURRENCY_MULTIPLIER);
  const paidAmount = paidAmountInCents / CURRENCY_MULTIPLIER;
  
  const difference = paidAmount - expectedAmount;
  const isValid = Math.abs(difference) <= 0.01;

  return {
    isValid,
    paidAmount,
    expectedAmount,
    difference
  };
}

function verifyPayPalAmount(paypalPayment, expectedAmount) {
  const paidAmount = parseFloat(paypalPayment.transactions[0].amount.total);
  const difference = paidAmount - expectedAmount;
  const isValid = Math.abs(difference) <= 0.01;

  return {
    isValid,
    paidAmount,
    expectedAmount,
    difference
  };
}
