/**
 * GraphQL权限系统验证测试
 * 验证权限配置的完整性和正确性
 */

const { 
  WHATSAPP_TOKEN_ALLOWED_OPERATIONS,
  PUBLIC_OPERATIONS,
  SENSITIVE_OPERATIONS,
  USER_ROLES
} = require('../../../graphql/permissions/constants');

const permissions = require('../../../graphql/permissions');

describe('GraphQL权限系统验证测试', () => {
  
  describe('权限常量完整性测试', () => {
    test('WHATSAPP_TOKEN_ALLOWED_OPERATIONS应该包含正确的操作', () => {
      // 验证包含必要的操作
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('customerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getAddressFromPostcode');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getSessionByToken');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('placeOrderWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('addCustomerAddress');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('deleteCustomerAddress');
      
      // 验证不包含已移除的操作
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('placeOrderFromWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getCustomerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getRestaurantMenuForCustomer');
    });

    test('PUBLIC_OPERATIONS应该包含所有公开操作', () => {
      // 验证公开查询
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('restaurants');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('restaurant');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('categories');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('foods');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('banners');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('cuisines');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('brand');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('brands');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('nearByRestaurants');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('topRatedVendors');
      
      // 验证公开变更
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('createUser');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('login');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('adminLogin');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('restaurantLogin');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('riderLogin');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('forgotPassword');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('resetPassword');
    });

    test('SENSITIVE_OPERATIONS应该包含所有敏感操作', () => {
      // 验证敏感查询
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('users');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('configuration');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('allOrders');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('riders');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('earnings');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('withdrawRequests');
      
      // 验证敏感变更
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createVendor');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createRider');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createBrand');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('updateCommission');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('saveConfiguration');
      
      // 验证不包含已移除的操作
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('allUsers');
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('getRestaurantMenuForCustomer');
    });

    test('USER_ROLES应该定义所有用户角色', () => {
      expect(USER_ROLES).toHaveProperty('ADMIN');
      expect(USER_ROLES).toHaveProperty('RESTAURANT');
      expect(USER_ROLES).toHaveProperty('CUSTOMER');
      expect(USER_ROLES).toHaveProperty('RIDER');
    });
  });

  describe('权限配置结构测试', () => {
    test('permissions对象应该有正确的结构', () => {
      expect(permissions).toBeDefined();
      expect(typeof permissions).toBe('object');
    });

    test('权限配置应该包含Query、Mutation和Subscription', () => {
      // 这里我们验证permissions对象的基本结构
      // 由于permissions是一个复杂的graphql-shield对象，我们主要验证它能正确导入
      expect(permissions).toBeTruthy();
    });
  });

  describe('权限操作覆盖测试', () => {
    test('应该覆盖所有重要的GraphQL操作类型', () => {
      // 验证WhatsApp Token操作数量合理
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS.length).toBeGreaterThan(5);
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS.length).toBeLessThan(20);
      
      // 验证公开操作数量合理
      expect(PUBLIC_OPERATIONS.QUERIES.length).toBeGreaterThan(20);
      expect(PUBLIC_OPERATIONS.MUTATIONS.length).toBeGreaterThan(5);
      
      // 验证敏感操作数量合理
      expect(SENSITIVE_OPERATIONS.QUERIES.length).toBeGreaterThan(10);
      expect(SENSITIVE_OPERATIONS.MUTATIONS.length).toBeGreaterThan(20);
    });

    test('不同权限类别之间不应该有重叠', () => {
      // WhatsApp Token操作不应该在敏感操作中
      const whatsappInSensitive = WHATSAPP_TOKEN_ALLOWED_OPERATIONS.some(op => 
        SENSITIVE_OPERATIONS.QUERIES.includes(op) || 
        SENSITIVE_OPERATIONS.MUTATIONS.includes(op)
      );
      expect(whatsappInSensitive).toBe(false);
      
      // 公开操作不应该在敏感操作中
      const publicInSensitive = [
        ...PUBLIC_OPERATIONS.QUERIES,
        ...PUBLIC_OPERATIONS.MUTATIONS
      ].some(op => 
        SENSITIVE_OPERATIONS.QUERIES.includes(op) || 
        SENSITIVE_OPERATIONS.MUTATIONS.includes(op)
      );
      expect(publicInSensitive).toBe(false);
    });
  });

  describe('权限配置一致性测试', () => {
    test('所有权限常量应该是数组', () => {
      expect(Array.isArray(WHATSAPP_TOKEN_ALLOWED_OPERATIONS)).toBe(true);
      expect(Array.isArray(PUBLIC_OPERATIONS.QUERIES)).toBe(true);
      expect(Array.isArray(PUBLIC_OPERATIONS.MUTATIONS)).toBe(true);
      expect(Array.isArray(SENSITIVE_OPERATIONS.QUERIES)).toBe(true);
      expect(Array.isArray(SENSITIVE_OPERATIONS.MUTATIONS)).toBe(true);
    });

    test('权限常量不应该包含空值或undefined', () => {
      WHATSAPP_TOKEN_ALLOWED_OPERATIONS.forEach(op => {
        expect(op).toBeTruthy();
        expect(typeof op).toBe('string');
      });
      
      PUBLIC_OPERATIONS.QUERIES.forEach(op => {
        expect(op).toBeTruthy();
        expect(typeof op).toBe('string');
      });
      
      PUBLIC_OPERATIONS.MUTATIONS.forEach(op => {
        expect(op).toBeTruthy();
        expect(typeof op).toBe('string');
      });
      
      SENSITIVE_OPERATIONS.QUERIES.forEach(op => {
        expect(op).toBeTruthy();
        expect(typeof op).toBe('string');
      });
      
      SENSITIVE_OPERATIONS.MUTATIONS.forEach(op => {
        expect(op).toBeTruthy();
        expect(typeof op).toBe('string');
      });
    });

    test('权限常量不应该有重复项', () => {
      // 检查WhatsApp Token操作
      const whatsappUnique = new Set(WHATSAPP_TOKEN_ALLOWED_OPERATIONS);
      expect(whatsappUnique.size).toBe(WHATSAPP_TOKEN_ALLOWED_OPERATIONS.length);
      
      // 检查公开查询
      const publicQueriesUnique = new Set(PUBLIC_OPERATIONS.QUERIES);
      expect(publicQueriesUnique.size).toBe(PUBLIC_OPERATIONS.QUERIES.length);
      
      // 检查公开变更
      const publicMutationsUnique = new Set(PUBLIC_OPERATIONS.MUTATIONS);
      expect(publicMutationsUnique.size).toBe(PUBLIC_OPERATIONS.MUTATIONS.length);
      
      // 检查敏感查询
      const sensitiveQueriesUnique = new Set(SENSITIVE_OPERATIONS.QUERIES);
      expect(sensitiveQueriesUnique.size).toBe(SENSITIVE_OPERATIONS.QUERIES.length);
      
      // 检查敏感变更
      const sensitiveMutationsUnique = new Set(SENSITIVE_OPERATIONS.MUTATIONS);
      expect(sensitiveMutationsUnique.size).toBe(SENSITIVE_OPERATIONS.MUTATIONS.length);
    });
  });

  describe('权限配置安全性测试', () => {
    test('关键管理操作应该在敏感操作列表中', () => {
      const criticalOperations = [
        'createVendor',
        'deleteVendor', 
        'createRider',
        'deleteRider',
        'updateCommission',
        'saveConfiguration',
        'createBrand',
        'deleteBrand'
      ];
      
      criticalOperations.forEach(op => {
        const isInSensitive = SENSITIVE_OPERATIONS.MUTATIONS.includes(op);
        expect(isInSensitive).toBe(true);
      });
    });

    test('用户管理操作应该在敏感操作列表中', () => {
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('users');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('deleteUser');
    });

    test('财务相关操作应该在敏感操作列表中', () => {
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('earnings');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('withdrawRequests');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createWithdrawRequest');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('updateWithdrawReqStatus');
    });
  });
});
