/**
 * GraphQL权限管理集成测试
 * 基于 test/cases/integration/graphql_permissions_integration_tests.yaml
 */

const { graphql } = require('graphql');
const { applyMiddleware } = require('graphql-middleware');
const graphqlTools = require('@graphql-tools/schema');
const jwt = require('jsonwebtoken');

const typeDefs = require('../../../graphql/schema');
const resolvers = require('../../../graphql/resolvers');
const permissions = require('../../../graphql/permissions');
const config = require('../../../config');
const { USER_ROLES } = require('../../../graphql/permissions/constants');

// 构建带有权限中间件的 Schema
const schema = graphqlTools.makeExecutableSchema({ typeDefs, resolvers });
const schemaWithPermissions = applyMiddleware(schema, permissions);

// 测试辅助函数 - 使用graphql直接执行查询
const executeGraphQL = async (query, variables = {}, context = {}) => {
  return await graphql({
    schema: schemaWithPermissions,
    source: query,
    variableValues: variables,
    contextValue: {
      req: { ...context },
      // 添加WhatsApp认证相关的上下文（直接在根级别）
      whatsAppAuth: context.whatsAppAuth || false,
      whatsAppCustomerId: context.whatsAppCustomerId || null
    }
  });
};

// JWT Token生成辅助函数
const generateJWT = (payload) => {
  return jwt.sign(payload, config.JWT_SECRET, { expiresIn: '1h' });
};

const generateExpiredJWT = (payload) => {
  return jwt.sign(payload, config.JWT_SECRET, { expiresIn: '-1h' });
};

// GraphQL查询定义
const QUERIES = {
  GET_RESTAURANTS: `
    query GetRestaurants {
      restaurants {
        _id
        name
        address
      }
    }
  `,
  GET_PROFILE: `
    query GetProfile {
      profile {
        _id
        name
        email
      }
    }
  `,
  GET_USERS: `
    query GetUsers {
      users {
        _id
        name
        email
        userType
      }
    }
  `,
  GET_ORDERS: `
    query GetOrders($restaurantId: ID) {
      orders(restaurantId: $restaurantId) {
        _id
        orderStatus
        orderAmount
      }
    }
  `,
  GET_CUSTOMER_ADDRESSES: `
    query GetCustomerAddresses($customerId: String) {
      customerAddresses(customerId: $customerId) {
        addressId
        formattedAddress
        city
      }
    }
  `,
  GET_RESTAURANT: `
    query GetRestaurant($id: ID!) {
      restaurant(id: $id) {
        _id
        name
        categories {
          _id
          title
          foods {
            _id
            title
            price
          }
        }
      }
    }
  `,
  GET_ORDER: `
    query GetOrder($id: String!) {
      order(id: $id) {
        _id
        orderId
        orderStatus
        orderAmount
        restaurantId
        customerId
      }
    }
  `
};

const MUTATIONS = {
  PLACE_ORDER_WHATSAPP: `
    mutation PlaceOrderWhatsApp(
      $restaurantId: ID!
      $customerId: String!
      $orderInput: [OrderInput!]!
      $paymentMethod: String!
      $orderDate: String!
      $isPickedUp: Boolean!
      $taxationAmount: Float!
      $deliveryCharges: Float!
      $itemsSubtotal: Float!
      $orderAmount: Float!
    ) {
      placeOrderWhatsApp(
        restaurantId: $restaurantId
        customerId: $customerId
        orderInput: $orderInput
        paymentMethod: $paymentMethod
        orderDate: $orderDate
        isPickedUp: $isPickedUp
        taxationAmount: $taxationAmount
        deliveryCharges: $deliveryCharges
        itemsSubtotal: $itemsSubtotal
        orderAmount: $orderAmount
      ) {
        _id
        orderStatus
        orderAmount
      }
    }
  `
};

describe('GraphQL权限管理集成测试', () => {
  
  // ===== JWT认证机制测试组 =====
  describe('JWT认证机制测试组', () => {
    
    // I-PERM-A01: JWT认证成功访问受保护资源
    test('I-PERM-A01: JWT认证成功访问受保护资源', async () => {
      const token = generateJWT({
        userId: 'test-user-123',
        userType: USER_ROLES.CUSTOMER
      });

      const context = {
        isAuth: true,
        userId: 'test-user-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      // 验证权限检查通过（即使可能因为数据库中没有用户而失败）
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-A02: JWT认证失败拒绝访问
    test('I-PERM-A02: JWT认证失败拒绝访问', async () => {
      const context = {
        isAuth: false
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });

    // I-PERM-A03: 未认证用户访问公开资源
    test('I-PERM-A03: 未认证用户访问公开资源', async () => {
      const context = {};

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_RESTAURANTS,
        {},
        context
      );

      // 验证权限检查通过（公开资源不需要认证）
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-A04: 未认证用户访问受保护资源被拒绝
    test('I-PERM-A04: 未认证用户访问受保护资源被拒绝', async () => {
      const context = {};

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });
  });

  // ===== 角色权限测试组 =====
  describe('角色权限测试组', () => {
    
    // I-PERM-B01: 管理员访问管理功能
    test('I-PERM-B01: 管理员访问管理功能', async () => {
      const context = {
        isAuth: true,
        userId: 'admin-user-123',
        userType: USER_ROLES.ADMIN
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      // 验证权限检查通过（管理员可以访问用户列表）
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-B02: 非管理员访问管理功能被拒绝
    test('I-PERM-B02: 非管理员访问管理功能被拒绝', async () => {
      const context = {
        isAuth: true,
        userId: 'customer-user-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
      expect(data.users).toBeNull();
    });

    // I-PERM-B03: 餐厅用户访问餐厅功能
    test('I-PERM-B03: 餐厅用户访问餐厅功能', async () => {
      const restaurantId = 'restaurant-123';
      const context = {
        isAuth: true,
        userId: 'restaurant-user-123',
        userType: USER_ROLES.RESTAURANT,
        restaurantId: restaurantId
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDERS,
        { restaurantId: restaurantId },
        context
      );

      // 验证没有因为角色错误而被拒绝
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-B04: 餐厅用户访问其他餐厅资源被拒绝
    test('I-PERM-B04: 餐厅用户访问其他餐厅资源被拒绝', async () => {
      const context = {
        isAuth: true,
        userId: 'restaurant-user-123',
        userType: USER_ROLES.RESTAURANT,
        restaurantId: 'restaurant-123'
      };

      // 尝试访问管理员专用功能
      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
    });

    // I-PERM-B05: 客户用户访问自己的资源
    test('I-PERM-B05: 客户用户访问自己的资源', async () => {
      const context = {
        isAuth: true,
        userId: 'customer-user-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      // 验证没有因为角色错误而被拒绝
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-B06: 客户用户访问其他用户资源被拒绝
    test('I-PERM-B06: 客户用户访问其他用户资源被拒绝', async () => {
      const context = {
        isAuth: true,
        userId: 'customer-user-123',
        userType: USER_ROLES.CUSTOMER
      };

      // 尝试访问管理员功能（代表其他用户资源）
      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
      expect(data.users).toBeNull();
    });
  });

  // ===== WhatsApp Token权限测试组 =====
  describe('WhatsApp Token权限测试组', () => {

    // I-PERM-C01: WhatsApp Token访问允许的操作
    test('I-PERM-C01: WhatsApp Token访问允许的操作', async () => {
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'whatsapp-customer-123'
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_CUSTOMER_ADDRESSES,
        { customerId: 'whatsapp-customer-123' },
        context
      );

      // 验证没有因为权限错误而被拒绝
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-C02: WhatsApp Token提交订单
    test('I-PERM-C02: WhatsApp Token提交订单', async () => {
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'whatsapp-customer-123'
      };

      const orderVariables = {
        restaurantId: 'restaurant-123',
        customerId: 'whatsapp-customer-123',
        orderInput: [{ food: 'food-123', quantity: 1, variation: '' }],
        paymentMethod: 'CASH',
        orderDate: new Date().toISOString(),
        isPickedUp: false,
        taxationAmount: 0.99,
        deliveryCharges: 2.50,
        itemsSubtotal: 10.00,
        orderAmount: 13.49
      };

      const { data, errors } = await executeGraphQL(
        MUTATIONS.PLACE_ORDER_WHATSAPP,
        orderVariables,
        context
      );

      // 验证没有因为权限错误而被拒绝
      const isAuthError = errors?.some(e =>
        e.message.includes('用户需要登录') ||
        e.message.includes('用户管理需要管理员权限') ||
        e.message.includes('需要有效的X-WhatsAppW-Token认证')
      );
      expect(isAuthError).toBeFalsy();
    });

    // I-PERM-C03: WhatsApp Token访问未授权操作被拒绝
    test('I-PERM-C03: WhatsApp Token访问未授权操作被拒绝', async () => {
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'whatsapp-customer-123'
      };

      // 尝试访问管理员专用的用户列表查询
      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
    });

    // I-PERM-C04: 无效WhatsApp Token被拒绝
    test('I-PERM-C04: 无效WhatsApp Token被拒绝', async () => {
      const context = {
        whatsAppAuth: false,
        whatsAppCustomerId: null
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_CUSTOMER_ADDRESSES,
        { customerId: 'customer-123' },
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('需要有效的X-WhatsAppW-Token认证');
    });
  });

  // ===== 内部调用权限测试组 =====
  describe('内部调用权限测试组', () => {

    // I-PERM-D01: 带customerId的内部调用成功
    test('I-PERM-D01: 带customerId的内部调用成功', async () => {
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'customer-123'
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_CUSTOMER_ADDRESSES,
        { customerId: 'customer-123' },
        context
      );

      // 验证没有因为内部调用权限而被拒绝
      const isInternalCallError = errors?.some(e => e.message.includes('仅允许内部调用'));
      expect(isInternalCallError).toBeFalsy();
    });

    // I-PERM-D02: 不带customerId的内部调用被拒绝
    test('I-PERM-D02: 不带customerId的内部调用被拒绝', async () => {
      const context = {};

      // 尝试在没有认证的情况下访问需要认证的查询
      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
    });
  });

  // ===== 安全边界测试组 =====
  describe('安全边界测试组', () => {

    // I-PERM-E01: 权限提升攻击防护
    test('I-PERM-E01: 权限提升攻击防护', async () => {
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'whatsapp-customer-123'
      };

      // WhatsApp Token尝试访问管理员功能
      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
      expect(data.users).toBeNull();
    });

    // I-PERM-E02: 跨用户资源访问防护
    test('I-PERM-E02: 跨用户资源访问防护', async () => {
      // 用户A尝试访问需要管理员权限的用户列表（代表其他用户资源）
      const context = {
        isAuth: true,
        userId: 'user-a-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
      expect(data.users).toBeNull();
    });

    // I-PERM-E03: Token格式验证
    test('I-PERM-E03: Token格式验证', async () => {
      // 模拟格式错误的token（通过设置isAuth为false来模拟）
      const context = {
        isAuth: false,
        tokenFormatError: true
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });

    // I-PERM-E04: 空token处理
    test('I-PERM-E04: 空token处理', async () => {
      const context = {};

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });
  });

  // ===== 额外的边界测试 =====
  describe('额外的边界测试', () => {

    // 测试过期JWT token
    test('过期JWT Token被拒绝', async () => {
      const expiredToken = generateExpiredJWT({
        userId: 'test-user-123',
        userType: USER_ROLES.CUSTOMER
      });

      const context = {
        isAuth: false, // 过期token会导致认证失败
        tokenExpired: true
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });

    // 测试无效用户类型
    test('无效用户类型被拒绝', async () => {
      const context = {
        isAuth: true,
        userId: 'test-user-123',
        userType: 'INVALID_ROLE'
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_USERS,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户管理需要管理员权限');
      expect(data.users).toBeNull();
    });

    // 测试WhatsApp Token与JWT Token的隔离
    test('WhatsApp Token与JWT Token权限隔离', async () => {
      // WhatsApp Token不能访问需要JWT认证的资源
      const context = {
        whatsAppAuth: true,
        whatsAppCustomerId: 'whatsapp-customer-123',
        isAuth: false // 没有JWT认证
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_PROFILE,
        {},
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('用户需要登录');
      expect(data.profile).toBeNull();
    });
  });

  // ===== 权限配置验证测试组 =====
  describe('权限配置验证测试组', () => {

    // I-PERM-D01: WHATSAPP_TOKEN_ALLOWED_OPERATIONS配置验证
    test('I-PERM-D01: WHATSAPP_TOKEN_ALLOWED_OPERATIONS配置验证', async () => {
      const { WHATSAPP_TOKEN_ALLOWED_OPERATIONS } = require('../../../graphql/permissions/constants');

      // 验证包含正确的操作
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('customerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getAddressFromPostcode');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('getSessionByToken');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('placeOrderWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('addCustomerAddress');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).toContain('deleteCustomerAddress');

      // 验证不包含已移除的操作
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('placeOrderFromWhatsApp');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getCustomerAddresses');
      expect(WHATSAPP_TOKEN_ALLOWED_OPERATIONS).not.toContain('getRestaurantMenuForCustomer');
    });

    // I-PERM-D02: PUBLIC_OPERATIONS配置验证
    test('I-PERM-D02: PUBLIC_OPERATIONS配置验证', async () => {
      const { PUBLIC_OPERATIONS } = require('../../../graphql/permissions/constants');

      // 验证公开查询
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('restaurants');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('categories');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('foods');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('banners');
      expect(PUBLIC_OPERATIONS.QUERIES).toContain('cuisines');

      // 验证公开变更
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('createUser');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('login');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('adminLogin');
      expect(PUBLIC_OPERATIONS.MUTATIONS).toContain('restaurantLogin');
    });

    // I-PERM-D03: SENSITIVE_OPERATIONS配置验证
    test('I-PERM-D03: SENSITIVE_OPERATIONS配置验证', async () => {
      const { SENSITIVE_OPERATIONS } = require('../../../graphql/permissions/constants');

      // 验证敏感查询
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('users');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('configuration');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('allOrders');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('riders');
      expect(SENSITIVE_OPERATIONS.QUERIES).toContain('earnings');

      // 验证敏感变更
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createVendor');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createRider');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('createBrand');
      expect(SENSITIVE_OPERATIONS.MUTATIONS).toContain('updateCommission');

      // 验证不包含已移除的操作
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('allUsers');
      expect(SENSITIVE_OPERATIONS.QUERIES).not.toContain('getRestaurantMenuForCustomer');
    });
  });

  // ===== 订单权限测试组 =====
  describe('订单权限测试组', () => {

    // I-PERM-ORDER-01: 管理员访问所有订单
    test('I-PERM-ORDER-01: 管理员访问所有订单', async () => {
      const context = {
        isAuth: true,
        userId: 'admin-user-123',
        userType: USER_ROLES.ADMIN
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDER,
        { id: 'test-order-123' },
        context
      );

      // 验证管理员权限检查通过（即使可能因为数据库中没有订单而失败）
      const isPermissionError = errors?.some(e =>
        e.message.includes('需要标准的JWT用户认证') ||
        e.message.includes('只能访问自己餐厅的订单') ||
        e.message.includes('只能访问自己的订单') ||
        e.message.includes('无权访问订单')
      );
      expect(isPermissionError).toBeFalsy();
    });

    // I-PERM-ORDER-02: 餐厅用户访问自己餐厅的订单
    test('I-PERM-ORDER-02: 餐厅用户访问自己餐厅的订单', async () => {
      const context = {
        isAuth: true,
        userId: 'restaurant-user-123',
        userType: USER_ROLES.RESTAURANT,
        restaurantId: 'restaurant-123'
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDER,
        { id: 'test-order-123' },
        context
      );

      // 验证餐厅用户权限检查通过
      const isPermissionError = errors?.some(e =>
        e.message.includes('需要标准的JWT用户认证') ||
        e.message.includes('只能访问自己餐厅的订单') ||
        e.message.includes('无权访问订单')
      );
      expect(isPermissionError).toBeFalsy();
    });

    // I-PERM-ORDER-03: 客户访问自己的订单
    test('I-PERM-ORDER-03: 客户访问自己的订单', async () => {
      const context = {
        isAuth: true,
        userId: 'customer-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDER,
        { id: 'test-order-123' },
        context
      );

      // 验证客户权限检查通过
      const isPermissionError = errors?.some(e =>
        e.message.includes('需要标准的JWT用户认证') ||
        e.message.includes('只能访问自己的订单') ||
        e.message.includes('无权访问订单')
      );
      expect(isPermissionError).toBeFalsy();
    });

    // I-PERM-ORDER-04: 未认证用户访问订单被拒绝
    test('I-PERM-ORDER-04: 未认证用户访问订单被拒绝', async () => {
      const context = {
        isAuth: false
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDER,
        { id: 'test-order-123' },
        context
      );

      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('需要标准的JWT用户认证');
      expect(data?.order || null).toBeNull();
    });

    // I-PERM-ORDER-05: 订单列表查询权限验证
    test('I-PERM-ORDER-05: 订单列表查询权限验证', async () => {
      const restaurantContext = {
        isAuth: true,
        userId: 'restaurant-user-123',
        userType: USER_ROLES.RESTAURANT,
        restaurantId: 'restaurant-123'
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDERS,
        { restaurantId: 'restaurant-123' },
        restaurantContext
      );

      // 验证餐厅用户可以查询订单列表
      const isPermissionError = errors?.some(e =>
        e.message.includes('需要标准的JWT用户认证') ||
        e.message.includes('无权访问其他餐厅的资源')
      );
      expect(isPermissionError).toBeFalsy();
    });

    // I-PERM-ORDER-06: 客户查询订单列表权限验证
    test('I-PERM-ORDER-06: 客户查询订单列表权限验证', async () => {
      const customerContext = {
        isAuth: true,
        userId: 'customer-123',
        userType: USER_ROLES.CUSTOMER
      };

      const { data, errors } = await executeGraphQL(
        QUERIES.GET_ORDERS,
        {},
        customerContext
      );

      // 验证客户用户可以查询订单列表（会通过isOrderOwner进行过滤）
      const isPermissionError = errors?.some(e =>
        e.message.includes('需要标准的JWT用户认证') ||
        e.message.includes('仅餐厅用户可访问') ||
        e.message.includes('仅客户用户可访问')
      );
      expect(isPermissionError).toBeFalsy();
    });
  });
});
