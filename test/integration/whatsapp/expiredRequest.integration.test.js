const request = require('supertest');
const sessionService = require('../../../whatsapp/services/sessionService');
const whatsappService = require('../../../whatsapp/services/whatsappService');
const logger = require('../../../helpers/logger');

// Mock external services
jest.mock('../../../whatsapp/services/whatsappService');
jest.mock('../../../helpers/logger');

// Create a simple Express app for testing
const express = require('express');
const webhookController = require('../../../whatsapp/controllers/webhookController');

const createTestApp = () => {
  const app = express();
  app.use(express.json());
  app.post('/webhook', webhookController.processIncomingMessage);
  return app;
};

describe('Expired Request Integration Tests', () => {
  let testApp;

  beforeEach(async () => {
    // Create fresh test app
    testApp = createTestApp();

    // Clear all mocks
    jest.clearAllMocks();

    // Mock logger methods
    logger.debug = jest.fn();
    logger.warn = jest.fn();
    logger.error = jest.fn();
    logger.info = jest.fn();

    // Mock whatsappService methods
    whatsappService.sendDialogueText = jest.fn().mockResolvedValue(true);
    whatsappService.getAccessToken = jest.fn().mockResolvedValue('mock-token');
  });

  afterEach(async () => {
    // Clean up any test sessions manually
    const testDialogueIds = [
      'integration-test-dialogue-123',
      'fail-test-dialogue-123'
    ];

    for (const dialogueId of testDialogueIds) {
      try {
        await sessionService.deleteSession(dialogueId);
      } catch (error) {
        // Ignore errors during cleanup
      }
    }
  });

  describe('Expired Order Request Flow', () => {
    const mockDialogueId = 'integration-test-dialogue-123';
    const mockRecipientId = 'integration-test-recipient-456';
    const mockBrandWhatsappId = 'integration-test-brand-789';

    test('should handle expired confirm_order request end-to-end', async () => {
      // Arrange
      const webhookPayload = {
        data: {
          id: 'test-message-001',
          type: 'messages',
          attributes: {
            type: 'messages.received',
            content: [{
              displayType: 'text',
              attrs: { text: 'Confirm Order' },
              payload: { externalID: 'confirm_order' }
            }]
          },
          relationships: {
            dialogue: {
              data: {
                id: mockDialogueId,
                type: 'dialogues'
              }
            },
            recipient: {
              data: {
                id: mockRecipientId,
                type: 'recipients'
              }
            },
            agent: {
              data: {
                id: mockBrandWhatsappId,
                type: 'agents'
              }
            }
          }
        }
      };

      // Ensure no session exists (simulating expired session)
      const existingSession = await sessionService.getSession(mockDialogueId);
      expect(existingSession).toBeNull();

      // Act
      const response = await request(testApp)
        .post('/webhook')
        .send(webhookPayload)
        .expect(200);

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(response.status).toBe(200);
      
      // Verify expired request was detected
      expect(logger.warn).toHaveBeenCalledWith('Expired request detected', {
        dialogueId: mockDialogueId,
        externalID: 'confirm_order',
        messageContent: 'Confirm Order'
      });

      // Verify new session was created
      const newSession = await sessionService.getSession(mockDialogueId);
      expect(newSession).toBeTruthy();
      expect(newSession.dialogueId).toBe(mockDialogueId);

      // Verify expired message was sent
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockDialogueId,
        'Your order has expired. Please place a new order.'
      );

      // Verify welcome message was sent (called by orderFsmActions.sendWelcomeMessage)
      // Note: This might be called multiple times due to welcome message logic
      expect(whatsappService.sendDialogueText).toHaveBeenCalled();
    });

    test('should handle expired cancel_order request', async () => {
      // Arrange
      const webhookPayload = {
        data: {
          id: 'test-message-002',
          type: 'messages',
          attributes: {
            type: 'messages.received',
            content: [{
              displayType: 'text',
              attrs: { text: 'Cancel Order' },
              payload: { externalID: 'cancel_order' }
            }]
          },
          relationships: {
            dialogue: {
              data: {
                id: mockDialogueId,
                type: 'dialogues'
              }
            },
            recipient: {
              data: {
                id: mockRecipientId,
                type: 'recipients'
              }
            },
            agent: {
              data: {
                id: mockBrandWhatsappId,
                type: 'agents'
              }
            }
          }
        }
      };

      // Act
      const response = await request(testApp)
        .post('/webhook')
        .send(webhookPayload)
        .expect(200);

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(logger.warn).toHaveBeenCalledWith('Expired request detected', {
        dialogueId: mockDialogueId,
        externalID: 'cancel_order',
        messageContent: 'Cancel Order'
      });

      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockDialogueId,
        'Your order has expired. Please place a new order.'
      );
    });

    test('should NOT treat non-order operations as expired requests', async () => {
      // Arrange
      const webhookPayload = {
        data: {
          id: 'test-message-003',
          type: 'messages',
          attributes: {
            type: 'messages.received',
            content: [{
              displayType: 'text',
              attrs: { text: 'Order History' },
              payload: { externalID: 'order_history' }
            }]
          },
          relationships: {
            dialogue: {
              data: {
                id: mockDialogueId,
                type: 'dialogues'
              }
            },
            recipient: {
              data: {
                id: mockRecipientId,
                type: 'recipients'
              }
            },
            agent: {
              data: {
                id: mockBrandWhatsappId,
                type: 'agents'
              }
            }
          }
        }
      };

      // Act
      const response = await request(testApp)
        .post('/webhook')
        .send(webhookPayload)
        .expect(200);

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(logger.warn).not.toHaveBeenCalledWith(
        'Expired request detected',
        expect.any(Object)
      );

      // Should process as normal message
      const newSession = await sessionService.getSession(mockDialogueId);
      expect(newSession).toBeTruthy();
    });

    test('should process normally when session exists for order operation', async () => {
      // Arrange - Create existing session first
      const existingSessionData = {
        dialogueId: mockDialogueId,
        recipientId: mockRecipientId,
        brandWhatsappId: mockBrandWhatsappId,
        customerPhone: '+1234567890',
        context: {
          customer: { customerId: 'existing-customer' },
          selectedRestaurantRef: { id: 'existing-restaurant' }
        }
      };

      await sessionService.createSession(mockDialogueId, existingSessionData);

      const webhookPayload = {
        data: {
          id: 'test-message-004',
          type: 'messages',
          attributes: {
            type: 'messages.received',
            content: [{
              displayType: 'text',
              attrs: { text: 'Confirm Order' },
              payload: { externalID: 'confirm_order' }
            }]
          },
          relationships: {
            dialogue: {
              data: {
                id: mockDialogueId,
                type: 'dialogues'
              }
            },
            recipient: {
              data: {
                id: mockRecipientId,
                type: 'recipients'
              }
            },
            agent: {
              data: {
                id: mockBrandWhatsappId,
                type: 'agents'
              }
            }
          }
        }
      };

      // Act
      const response = await request(testApp)
        .post('/webhook')
        .send(webhookPayload)
        .expect(200);

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(logger.warn).not.toHaveBeenCalledWith(
        'Expired request detected',
        expect.any(Object)
      );

      // Should NOT send expired message
      expect(whatsappService.sendDialogueText).not.toHaveBeenCalledWith(
        mockDialogueId,
        'Your order has expired. Please place a new order.'
      );
    });
  });

  describe('Error Handling', () => {
    test('should handle session creation failure gracefully', async () => {
      // Arrange
      const mockDialogueId = 'fail-test-dialogue-123';
      
      // Mock session creation to fail
      const originalCreateSession = sessionService.createSession;
      sessionService.createSession = jest.fn().mockResolvedValue(null);

      const webhookPayload = {
        data: {
          id: 'test-message-005',
          type: 'messages',
          attributes: {
            type: 'messages.received',
            content: [{
              displayType: 'text',
              attrs: { text: 'Confirm Order' },
              payload: { externalID: 'confirm_order' }
            }]
          },
          relationships: {
            dialogue: {
              data: {
                id: mockDialogueId,
                type: 'dialogues'
              }
            },
            recipient: {
              data: {
                id: 'test-recipient',
                type: 'recipients'
              }
            },
            agent: {
              data: {
                id: 'test-brand',
                type: 'agents'
              }
            }
          }
        }
      };

      // Act
      const response = await request(testApp)
        .post('/webhook')
        .send(webhookPayload)
        .expect(200);

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to create session for expired request',
        expect.objectContaining({
          dialogueId: mockDialogueId
        })
      );

      // Restore original method
      sessionService.createSession = originalCreateSession;
    });
  });
});
