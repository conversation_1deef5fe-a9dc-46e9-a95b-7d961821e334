---
TestModule: webhookController
TestSubModule: expiredRequest
Description: "Test cases for expired request handling in WebhookController"
TestType: "Unit"
Priority: "High"
Tags:
  - "expired-request"
  - "session-management"
  - "order-operations"
  - "whatsapp"

TestFunction: expiredRequestDetection
Cases:
  - CaseID: "U-WHC-EXP-001"
    Module: "webhookController"
    Description: "Should identify order operations correctly"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "order-detection"
    Precondition:
      - "System can differentiate between order and non-order operations"
    Steps:
      - "1. Test externalID 'confirm_order' - should be identified as order operation"
      - "2. Test externalID 'cancel_order' - should be identified as order operation"
      - "3. Test externalID 'order_history' - should NOT be identified as order operation"
      - "4. Test externalID 'change_address' - should NOT be identified as order operation"
      - "5. Test externalID 'help' - should NOT be identified as order operation"
    ExpectedResult:
      - "Order operations (confirm_order, cancel_order) return true"
      - "Non-order operations return false"

  - CaseID: "U-WHC-EXP-002"
    Module: "webhookController"
    Description: "Should detect expired request conditions correctly"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "expired-detection"
    Precondition:
      - "System can check session existence and operation type"
    Steps:
      - "1. Test: no session + confirm_order → should detect as expired"
      - "2. Test: no session + cancel_order → should detect as expired"
      - "3. Test: no session + order_history → should NOT detect as expired"
      - "4. Test: existing session + confirm_order → should NOT detect as expired"
      - "5. Test: no session + null externalID → should NOT detect as expired"
    ExpectedResult:
      - "Only combination of (no session + order operation) detected as expired"
      - "All other combinations return false"

  - CaseID: "U-WHC-EXP-003"
    Module: "webhookController"
    Description: "Should have required service dependencies available"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "dependencies"
    Precondition:
      - "All required services are properly mocked"
    Steps:
      - "1. Verify sessionService.getSession is available"
      - "2. Verify sessionService.queueSessionEvent is available"
      - "3. Verify logger methods are available"
    ExpectedResult:
      - "All required service methods are defined and accessible"

---
TestFunction: processIncomingMessage
Cases:
  - CaseID: "U-WHC-EXP-101"
    Module: "webhookController"
    Description: "Should process expired order request flow"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "integration"
      - "expired-flow"
    Precondition:
      - "Session does not exist for dialogueId"
      - "Message contains order operation externalID"
    Steps:
      - "1. Receive message with externalID 'confirm_order'"
      - "2. Check session existence - returns null"
      - "3. Detect as expired request"
      - "4. Create new session"
      - "5. Queue EXPIRED_REQUEST event"
      - "6. Skip normal MESSAGE_RECEIVED processing"
    ExpectedResult:
      - "EXPIRED_REQUEST event queued with correct data"
      - "New session created"
      - "Normal message processing bypassed"

  - CaseID: "U-WHC-EXP-102"
    Module: "webhookController"
    Description: "Should handle session creation failure for expired request"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Session creation fails"
    Steps:
      - "1. Detect expired request"
      - "2. Attempt to create new session - fails"
      - "3. Log error"
      - "4. Return without processing"
    ExpectedResult:
      - "Error logged with appropriate message"
      - "No event queued"
      - "Processing stops gracefully"

  - CaseID: "U-WHC-EXP-103"
    Module: "webhookController"
    Description: "Should process normally when session exists for order operation"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "normal-flow"
    Precondition:
      - "Valid session exists for dialogueId"
      - "Message contains order operation externalID"
    Steps:
      - "1. Receive message with externalID 'confirm_order'"
      - "2. Check session existence - returns valid session"
      - "3. Not detected as expired request"
      - "4. Process as normal MESSAGE_RECEIVED"
    ExpectedResult:
      - "No expired request detection"
      - "MESSAGE_RECEIVED event queued"
      - "Normal processing flow continues"

---
TestFunction: createNewSession
Cases:
  - CaseID: "U-WHC-EXP-201"
    Module: "webhookController"
    Description: "Should create new session with proper data structure"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-creation"
    Precondition:
      - "Valid dialogueId, recipientId, and brandWhatsappId provided"
    Steps:
      - "1. Call createNewSession with valid parameters"
      - "2. Fetch customer phone number"
      - "3. Get brand reference"
      - "4. Fetch customer information"
      - "5. Create session with proper context"
    ExpectedResult:
      - "New session created with complete context"
      - "Customer information populated"
      - "Restaurant selection logic applied"
      - "Address selection logic applied"

  - CaseID: "U-WHC-EXP-202"
    Module: "webhookController"
    Description: "Should handle customer phone retrieval failure"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Customer phone retrieval fails"
    Steps:
      - "1. Call createNewSession"
      - "2. Attempt to get customer phone - fails"
      - "3. Log error and return null"
    ExpectedResult:
      - "Error logged"
      - "Method returns null"
      - "No session created"

---
TestConfiguration:
  Environment: "test"
  MockLevel: "service"
  Dependencies:
    - "sessionService"
    - "whatsappService"
    - "logger"
    - "restaurantStore"
  TestData:
    dialogueId: "test-dialogue-123"
    recipientId: "test-recipient-456"
    brandWhatsappId: "test-brand-789"
    messageId: "test-message-001"
  ExpectedBehavior:
    - "Expired requests should be detected and handled gracefully"
    - "New sessions should be created for expired requests"
    - "EXPIRED_REQUEST events should be queued for processing"
    - "Normal flow should continue for valid sessions"
    - "Error conditions should be handled without crashes"
