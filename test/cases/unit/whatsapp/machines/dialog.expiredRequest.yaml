---
TestModule: dialogManager
TestSubModule: expiredRequest
Description: "Test cases for expired request handling in DialogManager"
TestType: "Unit"
Priority: "High"
Tags:
  - "expired-request"
  - "dialog-management"
  - "message-handling"
  - "whatsapp"

TestFunction: handleExpiredRequest
Cases:
  - CaseID: "U-DLG-EXP-001"
    Module: "dialogManager"
    Description: "Should handle expired order operation request"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "positive"
      - "order-expired"
    Precondition:
      - "Valid session and context provided"
      - "Event data contains order operation"
    Steps:
      - "1. Call handleExpiredRequest with confirm_order event"
      - "2. Log processing start"
      - "3. Send expired message"
      - "4. Send welcome message"
      - "5. Log successful completion"
    ExpectedResult:
      - "Correct expired message sent: 'Your order has expired. Please place a new order.'"
      - "Welcome message sent to restart flow"
      - "Processing logged correctly"

  - CaseID: "U-DLG-EXP-002"
    Module: "dialogManager"
    Description: "Should send correct expired message for order operation"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "message-content"
    Precondition:
      - "Event data has requestType: 'ORDER_OPERATION'"
    Steps:
      - "1. Process expired request with ORDER_OPERATION type"
      - "2. Verify message content"
    ExpectedResult:
      - "Exact message: 'Your order has expired. Please place a new order.'"
      - "Message sent to correct dialogueId"

  - CaseID: "U-DLG-EXP-003"
    Module: "dialogManager"
    Description: "Should send generic expired message for unknown request type"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "fallback-message"
    Precondition:
      - "Event data has unknown requestType"
    Steps:
      - "1. Process expired request with unknown type"
      - "2. Verify fallback message content"
    ExpectedResult:
      - "Generic message: 'Your request has expired. Please try again.'"
      - "Message sent to correct dialogueId"

  - CaseID: "U-DLG-EXP-004"
    Module: "dialogManager"
    Description: "Should send welcome message after expired message"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "welcome-flow"
    Precondition:
      - "Expired message sent successfully"
    Steps:
      - "1. Process expired request"
      - "2. Verify welcome message is called"
      - "3. Check parameters passed to welcome message"
    ExpectedResult:
      - "orderFsmActions.sendWelcomeMessage called"
      - "Correct session and context passed"

  - CaseID: "U-DLG-EXP-005"
    Module: "dialogManager"
    Description: "Should log successful handling"
    Importance: "Low"
    Status: "Implemented"
    Tags:
      - "positive"
      - "logging"
    Precondition:
      - "Request processed successfully"
    Steps:
      - "1. Process expired request"
      - "2. Verify success log"
    ExpectedResult:
      - "Success log with dialogueId and externalID"

  - CaseID: "U-DLG-EXP-006"
    Module: "dialogManager"
    Description: "Should handle cancel_order expired request"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "cancel-order"
    Precondition:
      - "Event data contains cancel_order externalID"
    Steps:
      - "1. Process cancel_order expired request"
      - "2. Verify same handling as confirm_order"
    ExpectedResult:
      - "Same expired message sent"
      - "Welcome message sent"
      - "Processing completed successfully"

  - CaseID: "U-DLG-EXP-007"
    Module: "dialogManager"
    Description: "Should handle errors gracefully"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "whatsappService.sendDialogueText throws error"
    Steps:
      - "1. Mock sendDialogueText to throw error"
      - "2. Process expired request"
      - "3. Verify error handling"
    ExpectedResult:
      - "Error logged with details"
      - "Error re-thrown for upstream handling"

  - CaseID: "U-DLG-EXP-008"
    Module: "dialogManager"
    Description: "Should send fallback error message on failure"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "negative"
      - "fallback-error"
    Precondition:
      - "Welcome message sending fails"
    Steps:
      - "1. Mock welcome message to fail"
      - "2. Process expired request"
      - "3. Verify fallback message sent"
    ExpectedResult:
      - "Fallback message: 'Sorry, we encountered an error. Please try again later.'"
      - "Error logged appropriately"

  - CaseID: "U-DLG-EXP-009"
    Module: "dialogManager"
    Description: "Should handle missing eventData gracefully"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "negative"
      - "missing-data"
    Precondition:
      - "Event data is empty or missing fields"
    Steps:
      - "1. Process request with empty eventData"
      - "2. Verify graceful handling"
    ExpectedResult:
      - "Generic expired message sent"
      - "Welcome message sent"
      - "No errors thrown"

  - CaseID: "U-DLG-EXP-010"
    Module: "dialogManager"
    Description: "Should truncate long message content in logs"
    Importance: "Low"
    Status: "Implemented"
    Tags:
      - "positive"
      - "log-formatting"
    Precondition:
      - "Message content longer than 50 characters"
    Steps:
      - "1. Process request with long message content"
      - "2. Verify log truncation"
    ExpectedResult:
      - "Message content truncated to 50 characters in logs"
      - "Processing continues normally"

---
TestFunction: processSessionEvent
Cases:
  - CaseID: "U-DLG-EXP-101"
    Module: "dialogManager"
    Description: "Should process EXPIRED_REQUEST event type"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "integration"
      - "event-processing"
    Precondition:
      - "EXPIRED_REQUEST event in event handlers"
      - "Valid session and event data"
    Steps:
      - "1. Create EXPIRED_REQUEST event"
      - "2. Call processSessionEvent"
      - "3. Verify event routing to handleExpiredRequest"
    ExpectedResult:
      - "Event processed through handleExpiredRequest"
      - "Expired message and welcome message sent"
      - "Session updated if needed"

---
TestConfiguration:
  Environment: "test"
  MockLevel: "service"
  Dependencies:
    - "whatsappService"
    - "orderFsmActions"
    - "logger"
    - "sessionService"
  TestData:
    session:
      dialogueId: "test-dialogue-123"
      id: "test-session-token"
      context:
        customer:
          customerId: "test-customer-456"
          name: "Test Customer"
          phone: "+**********"
        selectedRestaurantRef:
          id: "test-restaurant-789"
          name: "Test Restaurant"
    eventData:
      externalID: "confirm_order"
      requestType: "ORDER_OPERATION"
      messageContent: "Confirm Order"
      messageId: "test-message-001"
      messageType: "TEXT"
  ExpectedBehavior:
    - "Expired requests should trigger appropriate user messages"
    - "Welcome messages should restart the user flow"
    - "Error conditions should be handled gracefully"
    - "Logging should provide adequate debugging information"
    - "All message sending should use proper service methods"

---
TestCoverage:
  Functions:
    - "handleExpiredRequest"
    - "processSessionEvent (EXPIRED_REQUEST case)"
  Scenarios:
    - "Order operation expired requests"
    - "Unknown request type handling"
    - "Error conditions and fallbacks"
    - "Message content validation"
    - "Logging and debugging"
  EdgeCases:
    - "Missing event data"
    - "Service failures"
    - "Long message content"
    - "Multiple error conditions"
