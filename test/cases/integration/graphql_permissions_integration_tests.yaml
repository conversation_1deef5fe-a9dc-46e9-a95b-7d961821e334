Feature: GraphQL权限管理集成测试
---
# JWT认证机制测试组
TestFunction: jwtAuthenticationTests
Cases:
  - CaseID: "I-PERM-A01"
    Module: "graphql-permissions"
    Description: "JWT认证成功访问受保护资源"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "jwt-auth"
      - "protected-resource"
    Precondition:
      - "GraphQL server is running"
      - "Valid JWT token is available"
      - "Test user exists in database"
    Steps:
      - "Generate valid JWT token for test user"
      - "Send GraphQL query for user profile with Bearer token"
      - "Verify authentication middleware processes token"
      - "Verify user data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "User profile data is returned"
      - "No authentication errors"

  - CaseID: "I-PERM-A02"
    Module: "graphql-permissions"
    Description: "JWT认证失败拒绝访问"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "jwt-auth"
      - "invalid-token"
    Precondition:
      - "GraphQL server is running"
      - "Invalid/expired JWT token is available"
    Steps:
      - "Send GraphQL query for user profile with invalid token"
      - "Verify authentication middleware rejects token"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains authentication error"
      - "Error message indicates invalid token"
      - "No user data is returned"

  - CaseID: "I-PERM-A03"
    Module: "graphql-permissions"
    Description: "未认证用户访问公开资源"
    Importance: "Medium"
    Status: "Pending"
    Tags:
      - "positive"
      - "public-access"
      - "no-auth"
    Precondition:
      - "GraphQL server is running"
      - "Public restaurants data exists"
    Steps:
      - "Send GraphQL query for restaurants without token"
      - "Verify public access is allowed"
      - "Verify restaurant data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Restaurant list is returned"
      - "No authentication required"

  - CaseID: "I-PERM-A04"
    Module: "graphql-permissions"
    Description: "未认证用户访问受保护资源被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "protected-resource"
      - "no-auth"
    Precondition:
      - "GraphQL server is running"
    Steps:
      - "Send GraphQL query for user profile without token"
      - "Verify authentication middleware blocks access"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains authentication error"
      - "Error message indicates login required"
      - "No user data is returned"

---
# 角色权限测试组
TestFunction: rolePermissionTests
Cases:
  - CaseID: "I-PERM-B01"
    Module: "graphql-permissions"
    Description: "管理员访问管理功能"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "admin-role"
      - "management-access"
    Precondition:
      - "GraphQL server is running"
      - "Admin user exists with ADMIN role"
      - "Valid JWT token for admin user"
    Steps:
      - "Generate JWT token for admin user"
      - "Send GraphQL query for users list"
      - "Verify admin role permission check passes"
      - "Verify users data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Users list is returned"
      - "Admin access is granted"

  - CaseID: "I-PERM-B02"
    Module: "graphql-permissions"
    Description: "非管理员访问管理功能被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "customer-role"
      - "admin-access-denied"
    Precondition:
      - "GraphQL server is running"
      - "Regular user exists with default role"
      - "Valid JWT token for regular user"
    Steps:
      - "Generate JWT token for regular user"
      - "Send GraphQL query for users list"
      - "Verify admin role permission check fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates admin permission required"
      - "No users data is returned"

  - CaseID: "I-PERM-B03"
    Module: "graphql-permissions"
    Description: "餐厅用户访问餐厅功能"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "restaurant-role"
      - "restaurant-access"
    Precondition:
      - "GraphQL server is running"
      - "Restaurant user exists with RESTAURANT role"
      - "Valid JWT token for restaurant user"
      - "Restaurant orders exist in database"
    Steps:
      - "Generate JWT token for restaurant user"
      - "Send GraphQL query for restaurant orders"
      - "Verify restaurant role permission check passes"
      - "Verify restaurant orders data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Restaurant orders are returned"
      - "Only own restaurant orders are included"

  - CaseID: "I-PERM-B04"
    Module: "graphql-permissions"
    Description: "餐厅用户访问其他餐厅资源被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "restaurant-role"
      - "cross-restaurant-access"
    Precondition:
      - "GraphQL server is running"
      - "Restaurant user exists with RESTAURANT role"
      - "Valid JWT token for restaurant user"
      - "Other restaurant data exists"
    Steps:
      - "Generate JWT token for restaurant user"
      - "Send GraphQL query for other restaurant orders"
      - "Verify resource ownership check fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates access denied to other restaurant"
      - "No other restaurant data is returned"

  - CaseID: "I-PERM-B05"
    Module: "graphql-permissions"
    Description: "客户用户访问自己的资源"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "customer-role"
      - "self-access"
    Precondition:
      - "GraphQL server is running"
      - "Customer user exists with default role"
      - "Valid JWT token for customer user"
      - "Customer orders exist in database"
    Steps:
      - "Generate JWT token for customer user"
      - "Send GraphQL query for own orders"
      - "Verify customer role permission check passes"
      - "Verify own orders data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Customer orders are returned"
      - "Only own orders are included"

  - CaseID: "I-PERM-B06"
    Module: "graphql-permissions"
    Description: "客户用户访问其他用户资源被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "customer-role"
      - "cross-user-access"
    Precondition:
      - "GraphQL server is running"
      - "Customer user exists with default role"
      - "Valid JWT token for customer user"
      - "Other customer data exists"
    Steps:
      - "Generate JWT token for customer user"
      - "Send GraphQL query for other customer orders"
      - "Verify resource ownership check fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates access denied to other user data"
      - "No other customer data is returned"

---
# WhatsApp Token权限测试组
TestFunction: whatsappTokenPermissionTests
Cases:
  - CaseID: "I-PERM-C01"
    Module: "graphql-permissions"
    Description: "WhatsApp Token访问允许的操作"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "whatsapp-token"
      - "allowed-operation"
    Precondition:
      - "GraphQL server is running"
      - "Valid X-WhatsAppW-Token is available"
      - "Customer data exists for token"
    Steps:
      - "Send GraphQL query for getCustomerAddresses with X-WhatsAppW-Token"
      - "Verify WhatsApp token permission check passes"
      - "Verify customer addresses data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Customer addresses are returned"
      - "WhatsApp token access is granted"

  - CaseID: "I-PERM-C02"
    Module: "graphql-permissions"
    Description: "WhatsApp Token提交订单"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "whatsapp-token"
      - "order-creation"
    Precondition:
      - "GraphQL server is running"
      - "Valid X-WhatsAppW-Token is available"
      - "Restaurant and menu data exists"
    Steps:
      - "Send GraphQL mutation for placeOrderFromWhatsApp with X-WhatsAppW-Token"
      - "Verify WhatsApp token permission check passes"
      - "Verify order is created successfully"
    ExpectedResult:
      - "Response status is 200"
      - "Order is created and returned"
      - "Order status is PENDING"

  - CaseID: "I-PERM-C03"
    Module: "graphql-permissions"
    Description: "WhatsApp Token访问未授权操作被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "whatsapp-token"
      - "unauthorized-operation"
    Precondition:
      - "GraphQL server is running"
      - "Valid X-WhatsAppW-Token is available"
    Steps:
      - "Send GraphQL query for getRestaurantMenuForCustomer with X-WhatsAppW-Token"
      - "Verify WhatsApp token permission check fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates operation not allowed for WhatsApp token"
      - "No restaurant menu data is returned"

  - CaseID: "I-PERM-C04"
    Module: "graphql-permissions"
    Description: "无效WhatsApp Token被拒绝"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "whatsapp-token"
      - "invalid-token"
    Precondition:
      - "GraphQL server is running"
      - "Invalid X-WhatsAppW-Token is available"
    Steps:
      - "Send GraphQL query for getCustomerAddresses with invalid X-WhatsAppW-Token"
      - "Verify WhatsApp token validation fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains authentication error"
      - "Error message indicates invalid WhatsApp token"
      - "No customer data is returned"

---
# 内部调用权限测试组
TestFunction: internalCallPermissionTests
Cases:
  - CaseID: "I-PERM-D01"
    Module: "graphql-permissions"
    Description: "带customerId的内部调用成功"
    Importance: "Medium"
    Status: "Pending"
    Tags:
      - "positive"
      - "internal-call"
      - "customer-id"
    Precondition:
      - "GraphQL server is running"
      - "Customer data exists"
      - "Restaurant menu data exists"
    Steps:
      - "Call getRestaurantMenuForCustomer with customerId parameter"
      - "Verify internal call permission check passes"
      - "Verify restaurant menu data is returned"
    ExpectedResult:
      - "Response status is 200"
      - "Restaurant menu is returned"
      - "Internal call is allowed"

  - CaseID: "I-PERM-D02"
    Module: "graphql-permissions"
    Description: "不带customerId的内部调用被拒绝"
    Importance: "Medium"
    Status: "Pending"
    Tags:
      - "negative"
      - "internal-call"
      - "missing-customer-id"
    Precondition:
      - "GraphQL server is running"
    Steps:
      - "Call getRestaurantMenuForCustomer without customerId parameter"
      - "Verify internal call permission check fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates internal call only"
      - "No restaurant menu data is returned"

---
# 权限配置验证测试组
TestFunction: permissionConfigurationValidationTests
Cases:
  - CaseID: "I-PERM-D01"
    Module: "graphql-permissions"
    Description: "WHATSAPP_TOKEN_ALLOWED_OPERATIONS配置验证"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "configuration"
      - "whatsapp-token"
    Precondition:
      - "Permission constants are loaded"
    Steps:
      - "Verify WHATSAPP_TOKEN_ALLOWED_OPERATIONS contains correct operations"
      - "Verify it includes customerAddresses, getAddressFromPostcode, getSessionByToken"
      - "Verify it includes placeOrderWhatsApp, addCustomerAddress, deleteCustomerAddress"
      - "Verify it does not contain removed operations"
    ExpectedResult:
      - "All expected operations are present"
      - "No removed operations are present"
      - "Operation names match schema definitions"

  - CaseID: "I-PERM-D02"
    Module: "graphql-permissions"
    Description: "PUBLIC_OPERATIONS配置验证"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "configuration"
      - "public-operations"
    Precondition:
      - "Permission constants are loaded"
    Steps:
      - "Verify PUBLIC_OPERATIONS.QUERIES contains all public queries"
      - "Verify it includes restaurants, categories, foods, banners, cuisines"
      - "Verify PUBLIC_OPERATIONS.MUTATIONS contains all public mutations"
      - "Verify it includes createUser, login, adminLogin, restaurantLogin"
    ExpectedResult:
      - "All public operations are correctly configured"
      - "No authentication required for public operations"

  - CaseID: "I-PERM-D03"
    Module: "graphql-permissions"
    Description: "SENSITIVE_OPERATIONS配置验证"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "positive"
      - "configuration"
      - "sensitive-operations"
    Precondition:
      - "Permission constants are loaded"
    Steps:
      - "Verify SENSITIVE_OPERATIONS contains all admin-only operations"
      - "Verify it includes users, configuration, allOrders, riders, earnings"
      - "Verify it includes createVendor, createRider, createBrand, updateCommission"
      - "Verify it does not contain removed operations like allUsers"
    ExpectedResult:
      - "All sensitive operations require admin privileges"
      - "No removed operations are present"

---
# 安全边界测试组
TestFunction: securityBoundaryTests
Cases:
  - CaseID: "I-PERM-E01"
    Module: "graphql-permissions"
    Description: "权限提升攻击防护"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "security"
      - "privilege-escalation"
    Precondition:
      - "GraphQL server is running"
      - "Valid X-WhatsAppW-Token is available"
    Steps:
      - "Send GraphQL query for admin functions with X-WhatsAppW-Token"
      - "Verify privilege escalation is blocked"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates insufficient privileges"
      - "No admin data is returned"

  - CaseID: "I-PERM-E02"
    Module: "graphql-permissions"
    Description: "跨用户资源访问防护"
    Importance: "High"
    Status: "Pending"
    Tags:
      - "negative"
      - "security"
      - "cross-user-access"
    Precondition:
      - "GraphQL server is running"
      - "Two different customer users exist"
      - "Valid JWT token for user A"
    Steps:
      - "User A attempts to access User B's orders"
      - "Verify cross-user access is blocked"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains permission error"
      - "Error message indicates access denied"
      - "No other user data is returned"

  - CaseID: "I-PERM-E03"
    Module: "graphql-permissions"
    Description: "Token格式验证"
    Importance: "Medium"
    Status: "Pending"
    Tags:
      - "negative"
      - "security"
      - "token-format"
    Precondition:
      - "GraphQL server is running"
    Steps:
      - "Send GraphQL query with malformed token"
      - "Verify token format validation fails"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains authentication error"
      - "Error message indicates invalid token format"
      - "No protected data is returned"

  - CaseID: "I-PERM-E04"
    Module: "graphql-permissions"
    Description: "空token处理"
    Importance: "Medium"
    Status: "Pending"
    Tags:
      - "negative"
      - "security"
      - "empty-token"
    Precondition:
      - "GraphQL server is running"
    Steps:
      - "Send GraphQL query with empty Authorization header"
      - "Verify empty token handling"
      - "Verify error response is returned"
    ExpectedResult:
      - "Response contains authentication error"
      - "Error message indicates authentication required"
      - "No protected data is returned"
