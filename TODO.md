# Firespoon API 安全改进 TODO

## 🔴 紧急优先级 (Critical)

### 1. JWT Token 安全改进
- [ ] **实现双Token机制**
  - [ ] 添加 Access Token 过期时间（30分钟）
  - [ ] 实现 Refresh Token 机制（7天）
  - [ ] 创建 RefreshToken 数据模型
  - [ ] 实现 token 刷新 API
  - [ ] 添加 token 撤销机制
  - [ ] 创建 token 黑名单系统

- [ ] **修改现有登录 resolvers**
  - [ ] `ownerLogin` - 添加双token支持
  - [ ] `login` - 添加双token支持  
  - [ ] `riderLogin` - 添加双token支持
  - [ ] `restaurantLogin` - 添加双token支持

- [ ] **前端适配**
  - [ ] 实现自动token刷新机制
  - [ ] 处理token过期错误
  - [ ] 更新API客户端代码

### 2. 默认密码安全
- [ ] **强制修改默认admin密码**
  - [ ] 检测默认密码使用
  - [ ] 首次登录强制修改密码
  - [ ] 添加密码复杂度验证
  - [ ] 实现密码历史记录（防止重复使用）

## 🟡 高优先级 (High)

### 3. 操作审计日志系统
- [ ] **数据模型设计**
  - [ ] 创建 AuditLog Schema
  - [ ] 设计索引策略
  - [ ] 实现数据分区（按时间）

- [ ] **审计中间件**
  - [ ] GraphQL 审计中间件
  - [ ] 自动记录高风险操作
  - [ ] 集成现有 resolvers

- [ ] **存储策略（Render部署）**
  - [ ] MongoDB Atlas 存储方案
  - [ ] 日志轮转策略
  - [ ] 数据归档机制

- [ ] **Sentry集成**
  - [ ] 高风险操作告警
  - [ ] 异常操作监控
  - [ ] 自定义Sentry事件

### 4. adminLogin 实现
- [ ] **选择实现方案**
  - [ ] 方案A：实现独立的adminLogin resolver
  - [ ] 方案B：移除adminLogin定义，统一使用ownerLogin
  - [ ] 更新GraphQL schema
  - [ ] 更新权限配置

## 🟢 中优先级 (Medium)

### 5. 权限系统增强
- [ ] **角色权限分离**
  - [ ] 考虑创建独立的Admin表
  - [ ] 实现基于角色的权限控制
  - [ ] 添加权限动态管理

- [ ] **API安全**
  - [ ] 实现请求频率限制
  - [ ] 添加IP白名单功能
  - [ ] 实现设备指纹识别

### 6. 监控和告警
- [ ] **安全监控**
  - [ ] 异常登录检测
  - [ ] 暴力破解防护
  - [ ] 实时安全告警

- [ ] **性能监控**
  - [ ] 审计日志性能影响评估
  - [ ] 数据库查询优化
  - [ ] 缓存策略实现

## 📋 实施计划

### 第一周
- [ ] JWT Token 双token机制实现
- [ ] 默认密码强制修改

### 第二周  
- [ ] 操作审计日志基础框架
- [ ] Sentry集成配置

### 第三周
- [ ] 审计日志完整实现
- [ ] adminLogin 问题解决

### 第四周
- [ ] 测试和优化
- [ ] 文档更新

## 🔧 技术细节

### Token 更新方案技术栈
```javascript
// 依赖包
- jsonwebtoken (已有)
- crypto (Node.js内置)

// 新增数据模型
- RefreshToken
- BlacklistedToken

// 新增API
- refreshToken mutation
- revokeToken mutation
```

### 审计日志技术栈
```javascript
// 数据存储
- MongoDB (主存储)
- MongoDB Atlas (Render部署推荐)

// 监控集成
- Sentry (错误和告警)
- Winston Logger (已有)

// 查询优化
- 复合索引
- 数据分区
- 归档策略
```

## 🚀 部署注意事项

### Render 平台特殊考虑
- [ ] 环境变量配置（JWT_SECRET, REFRESH_SECRET）
- [ ] MongoDB Atlas 连接配置
- [ ] Sentry DSN 配置
- [ ] 日志存储容量规划

### 安全配置
- [ ] CORS 策略更新
- [ ] HTTPS 强制使用
- [ ] 敏感数据加密存储

## 📊 成功指标

### 安全指标
- [ ] Token 泄露风险降低 90%
- [ ] 默认密码使用率 0%
- [ ] 安全事件响应时间 < 5分钟

### 性能指标  
- [ ] 审计日志对API性能影响 < 5%
- [ ] 日志查询响应时间 < 2秒
- [ ] 系统可用性 > 99.9%

## 📝 相关文档

- [ ] 安全架构文档更新
- [ ] API文档更新（新的token机制）
- [ ] 运维手册更新（审计日志管理）
- [ ] 应急响应流程文档

---

**负责人**: 开发团队  
**预计完成时间**: 4周  
**风险评估**: 中等（主要是向后兼容性）  
**回滚计划**: 保持旧token机制作为备选方案
