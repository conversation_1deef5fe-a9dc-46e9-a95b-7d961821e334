# GraphQL权限控制系统设计文档

## 概述

本文档描述了Firespoon API的GraphQL权限控制系统的重新设计。该系统基于对实际架构的深入理解，简化了权限架构，明确区分了不同的访问场景，提供了更加精确和高效的安全控制。

## 架构理解与设计原则

### 🎯 核心理解

经过深入分析，我们发现了一个关键的架构事实：

**WhatsApp系统调用不经过HTTP GraphQL端点**

```
真实的消息流程：
WhatsApp用户 → Payemoji → Webhook(X-MMC-signature) → 服务器内部处理 → 直接调用GraphQL resolvers
```

这意味着：
- WhatsApp系统的调用是服务器内部的直接函数调用
- 这些调用不会触发GraphQL权限规则
- 对WhatsApp系统进行GraphQL权限限制是无效的

### 🔐 重新设计的认证类型

基于上述理解，系统简化为以下认证机制：

1. **JWT认证** (`AUTH_TYPES.JWT`)
   - 标准的Bearer Token认证
   - 用于普通用户、餐厅用户、管理员等
   - 适用于所有标准的GraphQL HTTP请求

2. **X-WhatsAppW-Token认证** (`AUTH_TYPES.WEB_WHATSAPP`)
   - 使用 `X-WhatsAppW-Token` 请求头
   - **专门用于web页面的受限访问**
   - 严格限制只能访问特定操作：
     - `customerAddresses` - 查询用户地址
     - `getAddressFromPostcode` - 根据邮编获取地址
     - `getSessionByToken` - 获取会话信息
     - `placeOrderWhatsApp` - 提交订单
     - `addCustomerAddress` - 添加客户地址
     - `deleteCustomerAddress` - 删除客户地址

### ⚠️ 重要架构区分

**三种不同的访问模式**：

1. **Webhook认证** (X-MMC-signature)
   - 验证来自Payemoji的webhook请求
   - 不涉及GraphQL权限控制
   - 通过HMAC签名验证

2. **内部调用**
   - 服务器内部直接调用GraphQL resolvers
   - 通过customerId参数标识
   - 绕过所有HTTP权限检查

3. **Web页面请求** (X-WhatsAppW-Token)
   - 来自web页面的HTTP GraphQL请求
   - 需要严格的权限限制
   - 只能访问预定义的安全操作
   - **双重认证支持**：
     - 认证成功后设置 `ctx.whatsAppCustomerId` 和 `ctx.whatsAppAuth = true`
     - 用户类型设置为 `'default'` 而非 `'CUSTOMER'`
     - 权限规则支持通过 `whatsAppCustomerId` 识别客户身份
     - 可通过 `isCustomer` 和 `isSelfManagement` 等基础权限检查

## 🔐 双重认证架构设计

### 认证上下文差异

系统支持两种不同的客户认证方式，每种方式在上下文中设置不同的字段：

#### JWT 认证上下文
```javascript
{
  req: {
    isAuth: true,
    userId: "customer-id-123",
    userType: "CUSTOMER",
    restaurantId: null
  },
  whatsAppAuth: false,
  whatsAppCustomerId: undefined
}
```

#### X-WhatsAppW-Token 认证上下文
```javascript
{
  req: {
    isAuth: false,           // 注意：X-WhatsAppW-Token 不设置 isAuth
    userId: undefined,       // 注意：不设置 userId
    userType: "default",     // 注意：设置为 "default" 而非 "CUSTOMER"
    restaurantId: null
  },
  whatsAppAuth: true,        // X-WhatsAppW-Token 特有
  whatsAppCustomerId: "customer-id-123"  // X-WhatsAppW-Token 特有
}
```

### 权限规则适配

为了支持双重认证，关键权限规则进行了适配：

#### isCustomer 规则适配
```javascript
// 支持两种客户认证方式
const isJwtCustomer = userType === USER_ROLES.CUSTOMER;
const isWhatsAppCustomer = !!whatsAppCustomerId && ctx.whatsAppAuth === true;
const isCustomerUser = isJwtCustomer || isWhatsAppCustomer;
```

#### isSelfManagement 规则适配
```javascript
// 双重用户ID获取
const currentUserId = userId || whatsAppCustomerId;

// 自我管理操作支持
if (!args.userId) {
  if (whatsAppCustomerId) return true;  // X-WhatsAppW-Token 用户
  if (userId && (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.RESTAURANT)) return true;  // JWT 用户
}
```

#### isOrderOwner 规则适配
```javascript
// 客户用户订单所有权检查（支持双重认证）
if (userType === USER_ROLES.CUSTOMER || whatsAppCustomerId) {
  const currentCustomerId = userId || whatsAppCustomerId;
  if (order.customerId !== currentCustomerId) {
    return new Error('只能访问自己的订单');
  }
}
```

## 权限规则设计

### 🛡️ 核心权限规则

#### 基础认证规则
- `isAuthenticated` - 检查JWT认证
  - 验证Bearer Token的有效性
  - 适用于所有需要用户身份验证的操作

#### 特殊认证规则（重新设计）
- `isWebWhatsAppToken` - X-WhatsAppW-Token认证（严格限制）
  - 仅允许访问预定义的安全操作
  - 专门用于web页面的受限访问
  - 包含操作白名单验证
  - 认证成功后设置userId和userType，使其能通过isCustomer等基础权限检查

- `allowInternalCall` - 内部调用权限
  - 通过customerId参数标识内部调用
  - 绕过所有HTTP权限检查
  - 用于系统内部的直接调用

#### 角色权限规则
- `isAdmin` - 管理员权限验证
- `isRestaurant` - 餐厅用户权限验证
- `isCustomer` - 客户用户权限验证（支持双重认证）
  - **JWT认证**：检查 `userType === USER_ROLES.CUSTOMER`
  - **X-WhatsAppW-Token认证**：检查 `whatsAppCustomerId` 存在且 `whatsAppAuth === true`
  - 两种认证方式都被识别为有效的客户用户

### 资源级规则

- `isResourceOwner` - 通用资源所有权检查
- `isOrderOwner` - 订单所有权检查（支持双重认证）
  - 管理员可以访问所有订单
  - 餐厅用户只能访问自己餐厅的订单（通过 `order.restaurantId` 匹配）
  - 客户用户只能访问自己的订单（通过 `order.customerId` 匹配）
  - 支持 JWT 和 X-WhatsAppW-Token 两种认证方式的客户
- `isSelfManagement` - 自我管理权限（支持双重认证）
  - **双重用户ID获取**：优先使用 `ctx.req.userId`，如果没有则使用 `ctx.whatsAppCustomerId`
  - **自我管理操作**：对于没有 `userId` 参数的操作，允许两种认证方式的自我管理
  - 支持 JWT 客户和 X-WhatsAppW-Token 客户的自我管理操作

## 权限映射

### Query权限

#### 公开查询（无需认证）
```graphql
# 餐厅相关
restaurants          # 获取餐厅列表
restaurant           # 获取餐厅详情

# 菜单相关
categories           # 获取分类列表
foods               # 获取食物列表
foodByCategory      # 按分类获取食物
```

#### X-WhatsAppW-Token专用查询（严格受限）
```graphql
customerAddresses        # 获取客户地址
getAddressFromPostcode  # 根据邮编获取地址
getSessionByToken       # 获取会话信息
# 注意：这些是允许X-WhatsAppW-Token访问的查询操作
# 用于web页面的受限访问，严格限制操作范围
# 通过 WHATSAPP_TOKEN_ALLOWED_OPERATIONS 常量控制
```

#### 内部调用专用查询（系统内部使用）
```graphql
customerbyPhone         # 通过电话号码查询客户（通过customerId参数）
customerbyPhoneAll      # 查询所有电话号码客户（通过customerId参数）
# 注意：这些操作只能通过内部调用访问，不能通过HTTP请求
# 用于WhatsApp系统的服务器内部直接调用
# 通过 allowInternalCall 规则控制，需要 customerId 参数
```

#### 客户查询（支持双重认证）
- `profile` - 用户配置文件（需要自我管理权限）
- `userFavourite` - 用户收藏（需要自我管理权限）

#### 订单查询（支持双重认证）
- `orders` - 订单列表（餐厅、客户、管理员都可以访问自己相关的订单）
- `order` - 订单详情（需要订单所有权检查）
- `orderDetails` - 订单详情（需要订单所有权检查）
- `getRefund` - 获取退款信息（需要订单所有权检查）
- `getOrderRefunds` - 获取订单退款列表（需要订单所有权检查）

#### 餐厅查询
- `restaurantOrders` - 餐厅订单列表（需要资源所有权检查）
- `ordersByRestId` - 按餐厅ID获取订单（需要资源所有权检查）
- `getDashboardTotal` - 仪表板数据（需要资源所有权检查）

#### 管理员专用查询
- `users` - 用户列表
- `configuration` - 系统配置
- `allUsers` - 所有用户

### 🔄 Mutation权限分类

#### 公开变更（无需认证）
```graphql
createUser           # 用户注册
login               # 用户登录
```

#### X-WhatsAppW-Token专用变更（严格受限）
```graphql
placeOrderWhatsApp      # 提交订单
addCustomerAddress      # 添加客户地址
deleteCustomerAddress   # 删除客户地址
# 注意：这些是允许X-WhatsAppW-Token访问的变更操作
# 专门用于web页面的受限操作，严格限制操作范围
# 通过 WHATSAPP_TOKEN_ALLOWED_OPERATIONS 常量控制
# 需要配合 isCustomer 和 isSelfManagement 权限检查
```

#### 客户变更（支持双重认证）
- `updateUser` - 更新用户信息（需要自我管理检查）
- `placeOrder` - 下单（需要客户权限和自我管理检查）
- `createAddress` - 创建地址（需要客户权限和自我管理检查）
- `editAddress` - 编辑地址（需要客户权限和自我管理检查）
- `deleteAddress` - 删除地址（需要客户权限和自我管理检查）
- `addFavourite` - 添加收藏（需要客户权限和自我管理检查）
- `changePassword` - 修改密码（需要自我管理检查）
- `reviewOrder` - 评价订单（需要客户权限和自我管理检查）

#### 餐厅变更
- `updateOrderStatus` - 更新订单状态（需要资源所有权检查）
- `refundOrder` - 退款（需要资源所有权检查）
- `createRestaurant` - 创建餐厅
- `editRestaurant` - 编辑餐厅（需要资源所有权检查）

#### 管理员专用变更
- `Deactivate` - 停用用户
- `saveConfiguration` - 保存系统配置
- `save*Configuration` - 各种配置保存操作
- `updateCommission` - 更新佣金

## 安全特性

### 1. 环境隔离
- **introspection**: 仅在 `local` 环境启用
- **playground**: 仅在 `local` 环境启用
- **debug**: 仅在非生产环境启用

### 2. 默认拒绝策略
- `fallbackRule: deny` - 所有未明确允许的操作都被拒绝
- 白名单方式的权限控制

### 3. 认证机制隔离
- X-WhatsAppW-Token认证权限受限，只能访问特定操作
- 内部调用（通过customerId参数）可以访问完整的WhatsApp功能
- 在单一认证规则中实现操作级别的权限控制

### 4. 资源级权限控制
- 餐厅用户只能访问自己的餐厅资源
- 客户只能访问自己的订单和个人信息
- 基于数据库查询的所有权验证

### 5. 审计日志
- 记录所有权限检查失败的尝试
- 记录用户认证成功和失败
- 详细的安全事件日志

## 使用示例

### X-WhatsAppW-Token用户查询地址（受限操作）
```graphql
# 请求头: X-WhatsAppW-Token: <token>
query GetCustomerAddresses {
  getCustomerAddresses {
    _id
    formattedAddress
    coordinates {
      latitude
      longitude
    }
  }
}
```

### 内部调用查询菜单（完整权限）
```graphql
query GetRestaurantMenu($restaurantId: ID!, $customerId: String!) {
  getRestaurantMenuForCustomer(
    restaurantId: $restaurantId,
    customerId: $customerId
  ) {
    _id
    name
    foods {
      _id
      title
      price
    }
  }
}
```

### 管理员查询系统配置
```graphql
# 请求头: Authorization: Bearer <jwt_token>
query GetConfiguration {
  configuration {
    _id
    currency
    deliveryRate
  }
}
```

## 测试

权限控制系统包含全面的测试用例：

- **单元测试**: `test/unit/graphql/permissions.test.js`
- **认证机制测试**: 测试不同认证类型的权限控制
- **安全测试**: 测试权限绕过和攻击场景

### 运行测试

```bash
# 运行权限测试
npm test test/unit/graphql/permissions.test.js

# 运行所有GraphQL测试
npm test test/unit/graphql/
```

## 维护和扩展

### 添加新的权限规则

1. 在 `graphql/permissions/rules.js` 中定义新规则
2. 在 `graphql/permissions.js` 中应用规则
3. 添加相应的测试用例

### 添加新的认证机制

1. 在 `graphql/permissions/constants.js` 中定义新的认证类型
2. 创建相应的认证中间件
3. 更新权限规则以支持新认证机制

### 安全最佳实践

1. **最小权限原则** - 只给用户必要的最小权限
2. **认证机制隔离** - 严格区分不同认证机制的使用场景
3. **定期审计** - 定期检查权限配置和日志
4. **测试覆盖** - 确保所有权限路径都有测试覆盖
5. **监控告警** - 监控异常的权限访问尝试

## 故障排除

### 常见问题

1. **权限被拒绝** - 检查用户角色和权限映射
2. **认证机制混淆** - 确认使用正确的认证类型
3. **资源访问失败** - 检查资源所有权规则
4. **认证失败** - 检查token和认证中间件

### 调试技巧

1. 启用debug模式查看详细权限检查日志
2. 检查GraphQL context中的认证信息
3. 验证权限规则的逻辑和参数
4. 确认使用正确的认证机制

## 🔧 安全配置

### 环境配置
```javascript
// app.js - Apollo Server配置
{
  introspection: config.NODE_ENV === 'local',  // 仅本地环境启用内省
  playground: config.NODE_ENV === 'local',     // 仅本地环境启用playground
}
```

### WhatsApp认证配置
```javascript
// middleware/whatsapp-graphql-auth.js
// X-WhatsAppW-Token认证成功后的上下文设置
return {
  ...context,
  req: {
    ...context.req,
    userId: customerId,        // 将customerId设为userId
    userType: 'default'        // 设置为customer用户类型
  },
  whatsAppAuth: true,
  whatsAppCustomerId: customerId,
  whatsAppSession: session,
  authType: 'WEB_WHATSAPP'
};
```

### 权限常量配置
```javascript
// graphql/permissions/constants.js
const WHATSAPP_TOKEN_ALLOWED_OPERATIONS = [
  // 查询操作
  'customerAddresses',         // 查询客户地址
  'getAddressFromPostcode',    // 根据邮编获取地址
  'getSessionByToken',         // 获取会话数据

  // 变更操作
  'placeOrderWhatsApp',        // 提交订单
  'addCustomerAddress',        // 添加地址
  'updateCustomerAddress',     // 更新地址（如果存在）
  'deleteCustomerAddress'      // 删除地址
];

// 用户角色定义
const USER_ROLES = {
  ADMIN: 'ADMIN',
  RESTAURANT: 'RESTAURANT',
  CUSTOMER: 'default',       // 注意：客户角色使用 'default'
  RIDER: 'RIDER',
  VENDOR: 'VENDOR'
};

// 认证类型
const AUTH_TYPES = {
  JWT: 'JWT',
  WEB_WHATSAPP: 'WEB_WHATSAPP'
};
```

### 安全检查清单
- [ ] 生产环境禁用introspection和playground
- [ ] X-WhatsAppW-Token仅限制在预定义操作
- [ ] 内部调用通过customerId参数验证
- [ ] 所有敏感操作需要适当的权限验证
- [ ] 定期审查权限映射和规则

## 📋 测试验证

### 权限测试覆盖
1. **基础认证测试** - JWT token验证
2. **X-WhatsAppW-Token测试** - 受限操作验证
3. **内部调用测试** - customerId参数验证
4. **角色权限测试** - 管理员、餐厅、客户权限
5. **拒绝访问测试** - 未授权操作拒绝

### 安全测试场景
- 无效token访问尝试
- 跨权限操作尝试
- 权限提升攻击测试
- 内省和playground访问控制

## 📝 更新日志

- **v2.1.0** (2025-08-15) - 实现双重认证架构支持
  - 更新 `isCustomer` 规则支持 JWT 和 X-WhatsAppW-Token 双重认证
  - 更新 `isSelfManagement` 规则支持双重用户ID获取和自我管理操作
  - 更新 `isOrderOwner` 规则支持双重认证的订单所有权检查
  - 修复订单权限逻辑，正确实现餐厅用户、客户和管理员的订单访问权限
  - 完善 X-WhatsAppW-Token 允许操作列表，支持地址管理功能
- **v2.0.0** - 重新设计权限架构，简化认证机制，明确区分访问场景
- **v1.1.0** - 区分Web WhatsApp和WhatsApp系统认证
- **v1.0.0** - 初始权限控制系统实现

## 🔧 架构演进说明

### v2.1.0 双重认证架构
本版本的核心改进是实现了真正的双重认证支持，解决了 X-WhatsAppW-Token 认证用户无法正常使用系统功能的问题。

**关键改进：**
1. **认证上下文统一处理** - 权限规则能够识别和处理两种不同的认证上下文
2. **用户身份统一识别** - 无论是 JWT 还是 X-WhatsAppW-Token 认证，都能正确识别客户身份
3. **权限检查一致性** - 两种认证方式的用户享有相同的权限级别和访问范围
4. **向后兼容性** - 保持现有 JWT 认证流程不受影响

**技术实现：**
- 权限规则支持多种用户ID获取方式（`userId` 或 `whatsAppCustomerId`）
- 客户身份识别支持多种认证标识（`userType` 或 `whatsAppAuth`）
- 自我管理操作支持无参数场景（如地址管理）
- 订单所有权检查支持双重认证的客户识别

这一架构设计确保了系统的灵活性和可扩展性，为未来可能的新认证方式提供了良好的基础。
