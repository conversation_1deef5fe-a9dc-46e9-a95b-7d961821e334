<script src="https://checkout.stripe.com/checkout.js"></script>
<script>

    var paymentStatus=false;

    // 获取订单ID参数
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id') || 'FXLCH-34'; // 保持原有默认值

    // 从服务端安全获取Stripe公钥
    fetch(`/stripe/config?orderId=${orderId}`)
        .then(response => response.json())
        .then(config => {
            var handler = StripeCheckout.configure({
                key: config.publishableKey, // 从服务端获取，不再硬编码
                image: 'http://icons.iconarchive.com/icons/visualpharm/must-have/256/User-icon.png',
                locale: 'auto',
                token: function(token) {
                    paymentStatus=true
                    fetch('http://localhost:8000/stripe/charge?id=' + orderId, {
                        method: 'POST',
                        mode: 'cors',
                        cache: 'no-cache',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        redirect: 'follow',
                        body: JSON.stringify(token)
                    })
                        .then(response => response.json())
                        .then(result => {
                            if(result.redirect)
                                window.location='http://localhost:8000/'+result.redirect
                                })
                        .catch(error => { alert(error) });
                },
            });

            window.onload = function() {
                handler.open({
                    image: 'http://icons.iconarchive.com/icons/visualpharm/must-have/256/User-icon.png',
                    name: 'enatega',
                description: 'description',
                amount: config.amount, // 从服务端获取金额
                    currency: 'USD',
                allowRememberMe: true,
                    email: '<EMAIL>',
                closed: function() {
                    if(!paymentStatus)
                        window.location='http://localhost:8000/stripe/cancel'
                }
            });
            };
        })
        .catch(error => {
            console.error('Failed to load config:', error);
            alert('Failed to load payment configuration');
        });

</script>