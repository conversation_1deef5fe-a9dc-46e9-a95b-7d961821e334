const Order = require('../models/order');
const Refund = require('../models/refund');
const { REFUND_STATUS, REFUND_TYPE, ORDER_STATUS } = require('../helpers/enum');
const refundCalculationService = require('./refundCalculationService');
const stripeRefundService = require('./stripeRefundService');
const logger = require('../helpers/logger');

class RefundService {
  /**
   * 发起退款
   * @param {String} orderId - 订单ID
   * @param {Number} amount - 退款金额（可选，全额退款时不需要）
   * @param {String} reason - 退款原因
   * @param {String} reasonText - 退款原因文本（可选）
   * @param {String} refundType - 退款类型 (FULL/PARTIAL)
   * @param {String} requestedBy - 发起人ID
   * @returns {Object} 退款结果
   */
  async initiateRefund(orderId, amount, reason, reasonText, refundType, requestedBy) {
    try {
      // 1. 查找并验证订单
      const order = await Order.findById(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // 2. 验证订单状态
      if (!this.canRefund(order)) {
        throw new Error('Order cannot be refunded in current status');
      }

      // 3. 验证支付状态
      if (order.paymentStatus !== 'PAID') {
        throw new Error('Order is not paid, cannot refund');
      }

      // 4. 计算退款金额
      let refundAmount;
      if (refundType === REFUND_TYPE.FULL) {
        refundAmount = order.orderAmount - (order.totalRefunded || 0);
      } else {
        refundAmount = amount;
      }

      // 5. 验证退款金额
      const validation = refundCalculationService.validateRefundAmount(order, refundAmount);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // 6. 计算最终退款金额和手续费
      const calculation = refundCalculationService.calculateRefundAmount(order, refundAmount, reason);

      // 7. 创建退款记录
      const refund = new Refund({
        refundId: Refund.generateRefundId(order.orderId), // 显式生成refundId
        orderId: order.orderId,
        originalOrderId: order._id,
        refundType,
        requestAmount: refundAmount,
        finalRefundAmount: calculation.finalRefundAmount,
        reason,
        reasonText,
        feeBearer: calculation.feeBearer,
        transactionFee: calculation.transactionFee,
        status: REFUND_STATUS.PENDING,
        requestedBy
      });

      await refund.save();

      // 8. 调用Stripe退款API
      try {
        await refund.markAsProcessing();
        
        const stripeRefund = await stripeRefundService.createRefund(
          order.paymentId || order.stripePaymentResponse?.payment_intent?.id,
          calculation.finalRefundAmount
        );

        refund.stripeRefundId = stripeRefund.id;
        await refund.save();

        logger.info('Refund initiated successfully', {
          refundId: refund.refundId,
          orderId: order.orderId,
          amount: calculation.finalRefundAmount,
          stripeRefundId: stripeRefund.id
        });

      } catch (stripeError) {
        await refund.markAsFailed(stripeError.message);
        throw new Error(`Stripe refund failed: ${stripeError.message}`);
      }

      // 9. 更新订单信息
      await this.updateOrderRefundInfo(order, refund);

      return {
        success: true,
        refund,
        order: await Order.findById(orderId).populate('refunds')
      };

    } catch (error) {
      logger.error('Refund initiation failed', {
        orderId,
        amount,
        reason,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 处理Stripe webhook回调
   * @param {String} stripeRefundId - Stripe退款ID
   * @param {String} status - 退款状态
   * @param {Object} webhookData - Webhook数据
   */
  async processRefundWebhook(stripeRefundId, status, webhookData) {
    try {
      const refund = await Refund.findByStripeRefundId(stripeRefundId);
      if (!refund) {
        logger.warn('Refund not found for Stripe webhook', { stripeRefundId });
        return;
      }

      const order = await Order.findById(refund.originalOrderId);
      if (!order) {
        logger.error('Order not found for refund webhook', { 
          refundId: refund.refundId,
          orderId: refund.orderId 
        });
        return;
      }

      if (status === 'succeeded') {
        await refund.markAsSucceeded();
        await this.finalizeRefund(order, refund);
        
        logger.info('Refund completed successfully', {
          refundId: refund.refundId,
          orderId: order.orderId,
          amount: refund.finalRefundAmount
        });

        // 发送成功通知
        await this.sendRefundNotification(order, refund, 'success');

      } else if (status === 'failed') {
        const errorMessage = webhookData.failure_reason || 'Unknown error';
        await refund.markAsFailed(errorMessage);
        
        logger.error('Refund failed', {
          refundId: refund.refundId,
          orderId: order.orderId,
          error: errorMessage
        });

        // 发送失败通知
        await this.sendRefundNotification(order, refund, 'failed');
      }

    } catch (error) {
      logger.error('Error processing refund webhook', {
        stripeRefundId,
        status,
        error: error.message
      });
    }
  }

  /**
   * 检查订单是否可以退款
   * @param {Object} order - 订单对象
   * @returns {Boolean}
   */
  canRefund(order) {
    const refundableStatuses = [
      ORDER_STATUS.PENDING,
      ORDER_STATUS.ACCEPTED,
      ORDER_STATUS.PICKED,
      ORDER_STATUS.DELIVERED,
      ORDER_STATUS.COMPLETED,
      ORDER_STATUS.PARTIALLY_REFUNDED
    ];
    
    return refundableStatuses.includes(order.orderStatus);
  }

  /**
   * 更新订单退款信息
   * @param {Object} order - 订单对象
   * @param {Object} refund - 退款对象
   */
  async updateOrderRefundInfo(order, refund) {
    // 添加退款记录到订单
    if (!order.refunds) {
      order.refunds = [];
    }
    order.refunds.push(refund._id);

    // 更新总退款金额
    order.totalRefunded = (order.totalRefunded || 0) + refund.finalRefundAmount;

    // 更新退款状态
    if (order.totalRefunded >= order.orderAmount) {
      order.refundStatus = 'FULL';
      if (refund.refundType === REFUND_TYPE.FULL) {
        order.orderStatus = ORDER_STATUS.CANCELLED;
      } else {
        order.orderStatus = ORDER_STATUS.REFUNDED;
      }
    } else {
      order.refundStatus = 'PARTIAL';
      order.orderStatus = ORDER_STATUS.PARTIALLY_REFUNDED;
    }

    await order.save();
  }

  /**
   * 完成退款处理
   * @param {Object} order - 订单对象
   * @param {Object} refund - 退款对象
   */
  async finalizeRefund(order, refund) {
    // 这里可以添加额外的完成逻辑
    // 比如更新库存、发送邮件等
    logger.info('Refund finalized', {
      refundId: refund.refundId,
      orderId: order.orderId
    });
  }

  /**
   * 发送退款通知
   * @param {Object} order - 订单对象
   * @param {Object} refund - 退款对象
   * @param {String} type - 通知类型 (success/failed)
   */
  async sendRefundNotification(order, refund, type) {
    // TODO: 实现通知逻辑
    // 这里应该调用通知服务发送短信/WhatsApp通知
    logger.info('Refund notification sent', {
      refundId: refund.refundId,
      orderId: order.orderId,
      type
    });
  }

  /**
   * 查询退款记录
   * @param {String} refundId - 退款ID
   * @returns {Object} 退款记录
   */
  async getRefund(refundId) {
    return await Refund.findOne({ refundId }).populate('originalOrderId');
  }

  /**
   * 查询订单的所有退款记录
   * @param {String} orderId - 订单ID
   * @returns {Array} 退款记录列表
   */
  async getOrderRefunds(orderId) {
    return await Refund.findByOrderId(orderId);
  }
}

module.exports = new RefundService();
