# Render平台审计日志存储策略

## 🎯 **推荐方案：MongoDB Atlas + Sentry**

### 主存储：MongoDB Atlas
```javascript
// 配置示例
const mongoConfig = {
  // Render环境变量
  uri: process.env.MONGODB_ATLAS_URI,
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10, // 连接池大小
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  }
};

// 审计日志专用数据库
const auditDB = mongoose.connection.useDb('firespoon_audit');
```

### 辅助存储：Sentry Events
```javascript
// 高风险事件实时告警
const sentryConfig = {
  dsn: process.env.SENTRY_DSN,
  environment: process.env.RENDER_SERVICE_NAME,
  release: process.env.RENDER_GIT_COMMIT
};
```

## 📊 **存储架构设计**

### 1. 数据分层存储
```
热数据 (0-3个月)    → MongoDB Atlas (主集群)
温数据 (3-12个月)   → MongoDB Atlas (归档集群)  
冷数据 (12个月+)    → 压缩存储 / 导出备份
实时告警           → Sentry Events
```

### 2. 集合设计
```javascript
// 按月分表策略
const getAuditCollection = (date = new Date()) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `audit_logs_${year}_${month}`;
};

// 示例：audit_logs_2024_01, audit_logs_2024_02
```

### 3. 索引策略
```javascript
// 复合索引优化查询性能
const indexes = [
  { 'timestamp': -1 },                    // 时间排序
  { 'actor.userId': 1, 'timestamp': -1 }, // 用户操作历史
  { 'action.category': 1, 'riskLevel': 1 }, // 风险分析
  { 'riskLevel': 1, 'timestamp': -1 },    // 高风险事件
  { 'action.operation': 1 },              // 操作类型统计
];
```

## 💰 **成本优化策略**

### MongoDB Atlas 成本控制
```javascript
// 1. 使用共享集群（开发/测试）
const devConfig = {
  tier: 'M0', // 免费层
  storage: '512MB',
  cost: '$0/month'
};

// 2. 生产环境专用集群
const prodConfig = {
  tier: 'M10', // 最小专用集群
  storage: '10GB',
  cost: '~$57/month',
  features: ['自动备份', '监控', '告警']
};
```

### 数据生命周期管理
```javascript
// 自动清理策略
const dataRetentionPolicy = {
  hotData: '3 months',    // 快速查询
  warmData: '9 months',   // 归档查询
  coldData: '3 years',    // 合规要求
  deletion: '7 years'     // 法律要求后删除
};

// 实现自动清理
const cleanupOldLogs = async () => {
  const cutoffDate = new Date();
  cutoffDate.setFullYear(cutoffDate.getFullYear() - 7);
  
  await AuditLog.deleteMany({
    timestamp: { $lt: cutoffDate }
  });
};
```

## 🔧 **Render部署配置**

### 环境变量设置
```bash
# MongoDB Atlas
MONGODB_ATLAS_URI=mongodb+srv://user:<EMAIL>/firespoon
MONGODB_AUDIT_URI=mongodb+srv://user:<EMAIL>/firespoon_audit

# Sentry
SENTRY_DSN=https://<EMAIL>/xxx
SENTRY_ENVIRONMENT=production

# 审计配置
AUDIT_LOG_LEVEL=HIGH
AUDIT_RETENTION_DAYS=2555  # 7年
AUDIT_BATCH_SIZE=100
```

### render.yaml 配置
```yaml
services:
  - type: web
    name: firespoon-api
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: MONGODB_ATLAS_URI
        fromDatabase:
          name: firespoon-db
          property: connectionString
      - key: SENTRY_DSN
        value: "your-sentry-dsn"
      - key: AUDIT_LOG_LEVEL
        value: "HIGH"

databases:
  - name: firespoon-db
    databaseName: firespoon
    user: firespoon_user
```

## 📈 **监控和告警**

### 1. 存储监控
```javascript
// MongoDB Atlas 监控指标
const storageMetrics = {
  diskUsage: 'monitor via Atlas dashboard',
  connectionCount: 'alert if > 80% of limit',
  queryPerformance: 'slow queries > 100ms',
  indexUsage: 'unused indexes cleanup'
};
```

### 2. Sentry 告警规则
```javascript
// 自定义告警规则
const sentryAlerts = [
  {
    name: 'High Risk Operations',
    condition: 'event.tags.riskLevel = CRITICAL',
    action: 'email + slack notification'
  },
  {
    name: 'Brute Force Attempts', 
    condition: 'event.tags.anomaly_type = brute_force_attempt',
    action: 'immediate notification'
  },
  {
    name: 'System Errors',
    condition: 'error rate > 5% in 5 minutes',
    action: 'escalate to on-call'
  }
];
```

## 🚀 **实施步骤**

### 第一阶段：基础设施
1. **设置MongoDB Atlas集群**
   ```bash
   # 创建专用数据库
   use firespoon_audit
   
   # 创建用户
   db.createUser({
     user: "audit_user",
     pwd: "secure_password",
     roles: ["readWrite"]
   })
   ```

2. **配置Sentry项目**
   ```bash
   # 安装Sentry CLI
   npm install -g @sentry/cli
   
   # 创建项目
   sentry-cli projects create firespoon-audit
   ```

### 第二阶段：代码集成
1. **安装依赖**
   ```bash
   npm install @sentry/node mongoose
   ```

2. **集成审计中间件**
   ```javascript
   // app.js
   const { SentryAuditIntegration } = require('./sentry_audit_integration');
   
   // 初始化
   SentryAuditIntegration.init();
   ```

### 第三阶段：测试和优化
1. **性能测试**
   - 审计日志写入性能
   - 查询响应时间
   - 存储空间增长率

2. **告警测试**
   - 模拟高风险操作
   - 验证Sentry通知
   - 测试自动清理

## 📋 **备选方案**

### 方案B：PostgreSQL + Sentry
```javascript
// 如果偏好关系型数据库
const pgConfig = {
  host: process.env.POSTGRES_HOST,
  database: 'firespoon_audit',
  table: 'audit_logs',
  partitioning: 'by_month' // 按月分区
};
```

### 方案C：外部日志服务
```javascript
// 使用专业日志服务
const logServices = [
  'Loggly',      // $99/month for 5GB
  'Papertrail',  // $7/month for 1GB  
  'LogDNA',      // $3/month for 1GB
];
```

## 🎯 **推荐配置**

**小型项目（<1000用户）**
- MongoDB Atlas M0 (免费)
- Sentry 免费计划
- 3个月数据保留

**中型项目（1000-10000用户）**  
- MongoDB Atlas M10 ($57/月)
- Sentry Team计划 ($26/月)
- 1年数据保留

**大型项目（>10000用户）**
- MongoDB Atlas M20+ ($134/月)
- Sentry Business计划 ($80/月)  
- 7年数据保留（合规要求）

这个方案既满足了安全需求，又控制了成本，非常适合Render平台部署！
